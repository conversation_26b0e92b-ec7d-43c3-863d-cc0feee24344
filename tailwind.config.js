/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./App.tsx",
    "./components/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: "class",
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        card: "var(--card)",
        "card-foreground": "var(--card-foreground)",
        popover: "var(--popover)",
        "popover-foreground": "var(--popover-foreground)",
        primary: "#5425b0",
        "primary-foreground": "var(--primary-foreground)",
        secondary: "var(--secondary)",
        "secondary-foreground": "var(--secondary-foreground)",
        muted: "var(--muted)",
        "muted-foreground": "var(--muted-foreground)",
        accent: "var(--accent)",
        "accent-foreground": "var(--accent-foreground)",
        destructive: "var(--destructive)",
        "destructive-foreground": "var(--destructive-foreground)",
        border: "var(--border)",
        input: "var(--input)",
        ring: "var(--ring)",

        // ViralHits theme colors - accessible via viral-* classes
        "viral-background": "var(--background)",
        "viral-foreground": "var(--foreground)",
        "viral-card": "var(--card)",
        "viral-card-foreground": "var(--card-foreground)",
        "viral-primary": "var(--primary)",
        "viral-primary-foreground": "var(--primary-foreground)",
        "viral-secondary": "var(--secondary)",
        "viral-secondary-foreground": "var(--secondary-foreground)",
        "viral-success": "var(--success)",
        "viral-success-foreground": "var(--success-foreground)",
        "viral-muted": "var(--muted)",
        "viral-muted-foreground": "var(--muted-foreground)",
        "viral-accent": "var(--accent)",
        "viral-accent-foreground": "var(--accent-foreground)",
        "viral-destructive": "var(--destructive)",
        "viral-destructive-foreground": "var(--destructive-foreground)",
        "viral-warning": "var(--warning)",
        "viral-warning-foreground": "var(--warning-foreground)",
        "viral-border": "var(--border)",
        "viral-input": "var(--input)",
        "viral-ring": "var(--ring)",
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      fontFamily: {
        heading: ["Montserrat", "sans-serif"],
        body: ["Open Sans", "sans-serif"],
        // ViralHits theme fonts
        "viral-inter": ["Inter", "sans-serif"],
        "viral-mono": ["JetBrains Mono", "monospace"],
      },
      fontSize: {
        xs: "var(--font-size-xs)",
        sm: "var(--font-size-sm)",
        base: "var(--font-size-base)",
        lg: "var(--font-size-lg)",
        /* …and so on… */
        // ViralHits theme font sizes
        "viral-base": "var(--font-size)",
      },
      animation: {
        // ViralHits theme animations
        "viral-float": "viral-hits-float 3s ease-in-out infinite",
        "viral-bounce-gentle":
          "viral-hits-bounce-gentle 2s ease-in-out infinite",
        "viral-fade-in-up": "viral-hits-fade-in-up 0.6s ease-out",
        "viral-pulse-soft": "viral-hits-pulse-soft 3s ease-in-out infinite",
        "viral-slide-in": "viral-hits-slide-in 0.8s ease-out",
        "viral-shimmer": "viral-hits-shimmer 2s linear infinite",
        "viral-glow": "viral-hits-glow 3s ease-in-out infinite",
        "viral-spin-slow": "viral-hits-spin-slow 8s linear infinite",
        "viral-data-flow": "viral-hits-data-flow 2s ease-in-out infinite",
      },
    },
  },
  plugins: [
    function ({ addUtilities }) {
      addUtilities({
        ".scrollbar-hide": {
          "-ms-overflow-style": "none",
          "scrollbar-width": "none",
          "&::-webkit-scrollbar": {
            display: "none",
          },
        },
        // ViralHits theme utilities
        ".viral-gradient-primary": {
          background: "linear-gradient(135deg, var(--primary), var(--accent))",
        },
        ".viral-gradient-secondary": {
          background:
            "linear-gradient(135deg, var(--secondary), var(--primary))",
        },
        ".viral-gradient-success": {
          background: "linear-gradient(135deg, var(--success), #34d399)",
        },
        ".viral-gradient-warning": {
          background: "linear-gradient(135deg, var(--warning), #fbbf24)",
        },
        ".viral-gradient-danger": {
          background: "linear-gradient(135deg, var(--destructive), #f87171)",
        },
        ".viral-bg-tech-gradient": {
          background:
            "linear-gradient(135deg, #fafafa 0%, #f3f4f6 50%, #e5e7eb 100%)",
        },
        ".viral-bg-accent-gradient": {
          background:
            "linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%)",
        },
        ".viral-bg-dark-gradient": {
          background: "linear-gradient(135deg, #1f2937 0%, #111827 100%)",
        },
        ".viral-modern-card": {
          background: "rgba(255, 255, 255, 0.95)",
          backdropFilter: "blur(10px)",
          border: "1px solid rgba(220, 38, 38, 0.1)",
          boxShadow: "0 4px 20px rgba(0, 0, 0, 0.05)",
        },
        ".viral-modern-card-hover": {
          transition: "all 0.3s ease",
          "&:hover": {
            background: "rgba(255, 255, 255, 1)",
            border: "1px solid rgba(220, 38, 38, 0.2)",
            transform: "translateY(-2px)",
            boxShadow: "0 8px 30px rgba(0, 0, 0, 0.08)",
          },
        },
      });
    },
  ],
};
