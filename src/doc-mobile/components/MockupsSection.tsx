export function MockupsSection() {
  const features = [
    {
      title: 'Provider Dashboard',
      description: 'Comprehensive medical dashboard for healthcare providers with patient management and analytics',
      icon: '👩‍⚕️'
    },
    {
      title: 'Patient Portal',
      description: 'User-friendly patient interface for appointments, records, and communication',
      icon: '📱'
    },
    {
      title: 'Telemedicine Hub',
      description: 'Secure video consultation platform with integrated medical tools',
      icon: '💬'
    },
    {
      title: 'Health Analytics',
      description: 'Advanced reporting and population health management dashboard',
      icon: '📊'
    }
  ];

  return (
    <section className="py-24 px-6 bg-gradient-to-b from-white to-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-blue-50 border border-blue-100 px-4 py-2 rounded-full mb-6">
            <span className="text-sm text-blue-600 font-medium">
              Final Design
            </span>
          </div>
          
          <h2 className="text-4xl font-bold mb-6 text-gray-900">
            Complete Healthcare Solution
          </h2>
          
          <p className="text-xl max-w-3xl mx-auto text-gray-600">
            A comprehensive healthcare platform designed for multiple user types and devices, 
            ensuring seamless care delivery across all touchpoints.
          </p>
        </div>

        {/* Desktop Dashboard Mockup */}
        <div className="relative mb-20">
          <div className="bg-gradient-to-br from-blue-50 via-white to-green-50 rounded-3xl p-12 lg:p-20">
            <div className="relative max-w-6xl mx-auto">
              {/* Desktop Frame */}
              <div className="relative">
                {/* Monitor */}
                <div className="bg-gray-900 rounded-t-3xl p-6 pb-0 shadow-2xl">
                  <div className="bg-white rounded-2xl overflow-hidden shadow-xl" style={{ aspectRatio: '16/9' }}>
                    {/* Dashboard Content */}
                    <div className="p-6 h-full">
                      {/* Header */}
                      <div className="flex items-center justify-between mb-6 pb-4 border-b border-gray-200">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-blue-500 rounded-xl flex items-center justify-center">
                            <span className="text-white font-bold">M</span>
                          </div>
                          <div>
                            <h3 className="font-bold text-gray-900">MedConnect Dashboard</h3>
                            <p className="text-sm text-gray-500">St. Mary's Hospital</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                            <span className="text-sm text-gray-600">Online</span>
                          </div>
                          <div className="w-8 h-8 bg-blue-100 rounded-full"></div>
                        </div>
                      </div>
                      
                      {/* Main Content Grid */}
                      <div className="grid grid-cols-12 gap-6 h-full">
                        {/* Left Panel - Patient List */}
                        <div className="col-span-4 bg-gray-50 rounded-2xl p-4">
                          <div className="flex items-center justify-between mb-4">
                            <h4 className="font-semibold text-gray-900">Today's Patients</h4>
                            <span className="text-sm bg-blue-100 text-blue-600 px-2 py-1 rounded-full">18</span>
                          </div>
                          <div className="space-y-3">
                            {[
                              { name: 'Sarah Johnson', time: '9:00 AM', status: 'waiting' },
                              { name: 'Mike Chen', time: '9:30 AM', status: 'in-progress' },
                              { name: 'Emily Davis', time: '10:00 AM', status: 'completed' },
                              { name: 'John Smith', time: '10:30 AM', status: 'scheduled' }
                            ].map((patient, index) => (
                              <div key={index} className="flex items-center gap-3 p-3 bg-white rounded-xl border border-gray-100">
                                <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                                <div className="flex-1">
                                  <div className="font-medium text-gray-900 text-sm">{patient.name}</div>
                                  <div className="text-xs text-gray-500">{patient.time}</div>
                                </div>
                                <div className={`w-2 h-2 rounded-full ${
                                  patient.status === 'completed' ? 'bg-green-400' :
                                  patient.status === 'in-progress' ? 'bg-blue-400' :
                                  patient.status === 'waiting' ? 'bg-orange-400' :
                                  'bg-gray-300'
                                }`}></div>
                              </div>
                            ))}
                          </div>
                        </div>
                        
                        {/* Center Panel - Patient Details */}
                        <div className="col-span-8 space-y-4">
                          {/* Patient Header */}
                          <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-2xl p-6 border border-blue-100">
                            <div className="flex items-center gap-4 mb-4">
                              <div className="w-16 h-16 bg-blue-500 rounded-2xl flex items-center justify-center">
                                <span className="text-white font-bold text-xl">MC</span>
                              </div>
                              <div>
                                <h3 className="text-xl font-bold text-gray-900">Mike Chen</h3>
                                <p className="text-gray-600">Age: 34 • Patient ID: #MC-2024-001</p>
                                <p className="text-sm text-blue-600">Cardiology Consultation</p>
                              </div>
                            </div>
                            
                            {/* Vital Signs */}
                            <div className="grid grid-cols-4 gap-4">
                              <div className="bg-white rounded-xl p-3 text-center border border-gray-100">
                                <div className="text-lg font-bold text-red-500">140/90</div>
                                <div className="text-xs text-gray-600">Blood Pressure</div>
                              </div>
                              <div className="bg-white rounded-xl p-3 text-center border border-gray-100">
                                <div className="text-lg font-bold text-blue-500">88</div>
                                <div className="text-xs text-gray-600">Heart Rate</div>
                              </div>
                              <div className="bg-white rounded-xl p-3 text-center border border-gray-100">
                                <div className="text-lg font-bold text-green-500">98.2°F</div>
                                <div className="text-xs text-gray-600">Temperature</div>
                              </div>
                              <div className="bg-white rounded-xl p-3 text-center border border-gray-100">
                                <div className="text-lg font-bold text-purple-500">98%</div>
                                <div className="text-xs text-gray-600">O2 Saturation</div>
                              </div>
                            </div>
                          </div>
                          
                          {/* Medical History */}
                          <div className="bg-white rounded-2xl p-6 border border-gray-100">
                            <h4 className="font-semibold text-gray-900 mb-4">Recent Medical History</h4>
                            <div className="space-y-3">
                              <div className="flex items-center gap-3">
                                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                                <span className="text-sm text-gray-700">Annual physical examination completed</span>
                                <span className="text-xs text-gray-400 ml-auto">2 weeks ago</span>
                              </div>
                              <div className="flex items-center gap-3">
                                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                                <span className="text-sm text-gray-700">Lab results: Cholesterol levels normal</span>
                                <span className="text-xs text-gray-400 ml-auto">1 month ago</span>
                              </div>
                              <div className="flex items-center gap-3">
                                <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                                <span className="text-sm text-gray-700">Cardiology referral for chest pain evaluation</span>
                                <span className="text-xs text-gray-400 ml-auto">6 weeks ago</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Monitor Base */}
                <div className="bg-gray-300 rounded-b-3xl h-12 relative shadow-lg">
                  <div className="absolute inset-x-0 top-0 h-2 bg-gray-400 rounded-full mx-auto w-32"></div>
                </div>
                
                {/* Monitor Stand */}
                <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 w-48 h-6 bg-gray-400 rounded-b-full shadow-md"></div>
              </div>
              
              {/* Floating Feature Cards */}
              <div className="absolute -top-8 -left-8 bg-white rounded-2xl p-4 shadow-xl max-w-xs hidden lg:block border border-gray-100">
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center">
                    <span>🏥</span>
                  </div>
                  <div>
                    <h4 className="text-sm font-semibold text-gray-900">
                      Real-time Patient Data
                    </h4>
                  </div>
                </div>
                <p className="text-xs text-gray-600">
                  Live vital signs monitoring
                </p>
              </div>
              
              <div className="absolute -top-8 -right-8 bg-white rounded-2xl p-4 shadow-xl max-w-xs hidden lg:block border border-gray-100">
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-8 h-8 bg-green-50 rounded-full flex items-center justify-center">
                    <span>📊</span>
                  </div>
                  <div>
                    <h4 className="text-sm font-semibold text-gray-900">
                      Clinical Analytics
                    </h4>
                  </div>
                </div>
                <p className="text-xs text-gray-600">
                  Advanced health insights
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Feature Highlights */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-50 to-green-50 rounded-2xl flex items-center justify-center mx-auto mb-4 border border-blue-100">
                <span className="text-2xl">{feature.icon}</span>
              </div>
              <h3 className="font-bold mb-3 text-gray-900">
                {feature.title}
              </h3>
              <p className="text-gray-600 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}