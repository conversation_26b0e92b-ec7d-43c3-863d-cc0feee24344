import finalMockup from 'figma:asset/81bd64851087386edb648821268ef5e26ba74d13.png';
import additionalMockup from 'figma:asset/f2b1b3101b752036f588f67b01ab4ed3a3546fd2.png';

export function StyleGuideSection() {
  return (
    <section className="py-24 px-6 bg-[#E6EEFF]">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-[#0052FD] px-6 py-3 rounded-full mb-8 shadow-lg">
            <span className="text-base text-white font-medium font-['DM_Sans',_sans-serif]">
              Design System
            </span>
          </div>
          
          <h2 className="text-5xl font-bold mb-8 text-[#1E1E1E] font-['DM_Sans',_sans-serif]">
            DocMobil Design System
          </h2>
        </div>

        {/* Colors Section - Main Focus */}
        <div className="bg-white rounded-3xl p-12 shadow-[0px_4px_19.4px_0px_rgba(0,0,0,0.16)]">
          <div className="text-left mb-12">
            <h2 className="text-3xl font-medium text-[#1E1E1E] font-['DM_Sans',_sans-serif] tracking-tight">
              COLORS
            </h2>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-5 gap-8 mb-16">
            {/* Primary Blue */}
            <div className="text-center">
              <div 
                className="w-32 h-32 rounded-full shadow-[0px_4px_19.4px_0px_rgba(0,0,0,0.16)] mb-6 mx-auto"
                style={{ backgroundColor: '#0052FD' }}
              ></div>
              <div className="text-[#1E1E1E] font-['DM_Sans',_sans-serif]">
                <div className="text-sm font-medium mb-2 opacity-70">PRIMARY</div>
                <div className="text-lg font-bold">#0052FD</div>
              </div>
            </div>

            {/* Success Green */}
            <div className="text-center">
              <div 
                className="w-32 h-32 rounded-full shadow-[0px_4px_19.4px_0px_rgba(0,0,0,0.16)] mb-6 mx-auto"
                style={{ backgroundColor: '#00E500' }}
              ></div>
              <div className="text-[#1E1E1E] font-['DM_Sans',_sans-serif]">
                <div className="text-sm font-medium mb-2 opacity-70">SUCCESS</div>
                <div className="text-lg font-bold">#00E500</div>
              </div>
            </div>

            {/* Neutral Gray */}
            <div className="text-center">
              <div 
                className="w-32 h-32 rounded-full shadow-[0px_4px_19.4px_0px_rgba(0,0,0,0.16)] mb-6 mx-auto"
                style={{ backgroundColor: '#E7E8E9' }}
              ></div>
              <div className="text-[#1E1E1E] font-['DM_Sans',_sans-serif]">
                <div className="text-sm font-medium mb-2 opacity-70">NEUTRAL</div>
                <div className="text-lg font-bold">#E7E8E9</div>
              </div>
            </div>

            {/* Dark */}
            <div className="text-center">
              <div 
                className="w-32 h-32 rounded-full shadow-[0px_4px_19.4px_0px_rgba(0,0,0,0.16)] mb-6 mx-auto"
                style={{ backgroundColor: '#1E1E1E' }}
              ></div>
              <div className="text-[#1E1E1E] font-['DM_Sans',_sans-serif]">
                <div className="text-sm font-medium mb-2 opacity-70">DARK</div>
                <div className="text-lg font-bold">#1E1E1E</div>
              </div>
            </div>

            {/* Light Blue */}
            <div className="text-center">
              <div 
                className="w-32 h-32 rounded-full shadow-[0px_4px_19.4px_0px_rgba(0,0,0,0.16)] mb-6 mx-auto border-2 border-[#E7E8E9]"
                style={{ backgroundColor: '#E6EEFF' }}
              ></div>
              <div className="text-[#1E1E1E] font-['DM_Sans',_sans-serif]">
                <div className="text-sm font-medium mb-2 opacity-70">LIGHT</div>
                <div className="text-lg font-bold">#E6EEFF</div>
              </div>
            </div>
          </div>

          {/* Typography Section within Colors */}
          <div className="border-t border-[#E7E8E9] pt-12 mb-16">
            <div className="grid lg:grid-cols-2 gap-16 items-start">
              <div>
                <div className="text-left mb-8">
                  <h3 className="text-3xl font-medium text-[#1E1E1E] font-['DM_Sans',_sans-serif] tracking-tight">
                    TYPOGRAPHY
                  </h3>
                  <div className="text-2xl font-bold text-[#1E1E1E] font-['DM_Sans',_sans-serif] mt-3">
                    DM Sans
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-8">
                  <div className="text-center">
                    <div className="text-[#1E1E1E] text-5xl font-normal font-['DM_Sans',_sans-serif] mb-3">
                      Aa
                    </div>
                    <div className="text-sm font-medium text-[#1E1E1E] opacity-70 font-['DM_Sans',_sans-serif]">
                      Regular
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-[#1E1E1E] text-5xl font-medium font-['DM_Sans',_sans-serif] mb-3">
                      Aa
                    </div>
                    <div className="text-sm font-medium text-[#1E1E1E] opacity-70 font-['DM_Sans',_sans-serif]">
                      Medium
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-[#1E1E1E] text-5xl font-bold font-['DM_Sans',_sans-serif] mb-3">
                      Aa
                    </div>
                    <div className="text-sm font-medium text-[#1E1E1E] opacity-70 font-['DM_Sans',_sans-serif]">
                      Bold
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-2xl font-bold text-[#1E1E1E] mb-6 font-['DM_Sans',_sans-serif]">
                  Color Usage
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <div className="w-6 h-6 rounded-full" style={{ backgroundColor: '#0052FD' }}></div>
                    <span className="text-[#1E1E1E] font-['DM_Sans',_sans-serif]">Primary actions, links, interactive elements</span>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="w-6 h-6 rounded-full" style={{ backgroundColor: '#00E500' }}></div>
                    <span className="text-[#1E1E1E] font-['DM_Sans',_sans-serif]">Success states, confirmations, positive feedback</span>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="w-6 h-6 rounded-full" style={{ backgroundColor: '#1E1E1E' }}></div>
                    <span className="text-[#1E1E1E] font-['DM_Sans',_sans-serif]">Primary text, headings, emphasis</span>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="w-6 h-6 rounded-full" style={{ backgroundColor: '#E7E8E9' }}></div>
                    <span className="text-[#1E1E1E] font-['DM_Sans',_sans-serif]">Neutral backgrounds, subtle dividers</span>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="w-6 h-6 rounded-full" style={{ backgroundColor: '#E6EEFF' }}></div>
                    <span className="text-[#1E1E1E] font-['DM_Sans',_sans-serif]">Light backgrounds, muted highlights</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Final Mockup */}
        <div className="mt-20 flex justify-center">
          <img 
            src={finalMockup} 
            alt="DocMobil Final Design" 
            className="w-full max-w-5xl h-auto object-contain"
          />
        </div>
        
        {/* Additional Mockup */}
        <div className="mt-20 flex justify-center">
          <img 
            src={additionalMockup} 
            alt="DocMobil Additional Design" 
            className="w-full max-w-5xl h-auto object-contain"
          />
        </div>
      </div>
    </section>
  );
}