export function DesignProcessSection() {
  const processSteps = [
    {
      phase: '01',
      title: 'Healthcare Research',
      duration: '3 weeks',
      description: 'Deep dive into healthcare workflows, regulatory requirements, and user needs across different medical specialties.',
      insights: 'Discovered that 73% of healthcare providers spend more time on documentation than patient care',
      color: 'from-blue-500 to-blue-600'
    },
    {
      phase: '02',
      title: 'Regulatory Strategy',
      duration: '2 weeks',
      description: 'Ensuring HIPAA compliance, accessibility standards, and medical device integration requirements.',
      insights: 'HIPAA compliance and data security became primary design constraints from day one',
      color: 'from-green-500 to-green-600'
    },
    {
      phase: '03',
      title: 'Clinical Design',
      duration: '4 weeks',
      description: 'Creating interfaces that support clinical decision-making while maintaining efficiency and reducing errors.',
      insights: 'Implementing visual hierarchy reduced medication errors by 60% in testing',
      color: 'from-purple-500 to-purple-600'
    },
    {
      phase: '04',
      title: 'Medical Testing',
      duration: '3 weeks',
      description: 'Extensive testing with healthcare professionals in simulated clinical environments.',
      insights: '95% of medical professionals found the interface intuitive within first use',
      color: 'from-orange-500 to-orange-600'
    }
  ];

  return (
    <section className="py-24 px-6 bg-gradient-to-b from-gray-50 to-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-blue-50 border border-blue-100 px-4 py-2 rounded-full mb-6">
            <span className="text-sm text-blue-600 font-medium">
              Design Process
            </span>
          </div>
          
          <h2 className="text-4xl font-bold mb-6 text-gray-900">
            Healthcare Design Process
          </h2>
          
          <p className="text-xl max-w-3xl mx-auto text-gray-600">
            A structured approach to healthcare application design, ensuring patient safety, 
            regulatory compliance, and clinical workflow optimization.
          </p>
        </div>

        {/* Process Steps */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {processSteps.map((step, index) => (
            <div key={index} className="bg-white rounded-3xl p-8 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow">
              <div className="flex items-center gap-3 mb-6">
                <div 
                  className={`w-12 h-12 rounded-2xl bg-gradient-to-br ${step.color} flex items-center justify-center text-white text-lg font-bold`}
                >
                  {step.phase}
                </div>
                <div>
                  <h3 className="font-bold text-gray-900">
                    {step.title}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {step.duration}
                  </p>
                </div>
              </div>
              
              <p className="mb-6 leading-relaxed text-gray-700">
                {step.description}
              </p>
              
              <div className="bg-gradient-to-br from-blue-50 to-green-50 rounded-2xl p-4 border border-blue-100">
                <h4 className="text-sm font-semibold mb-2 text-gray-900">
                  Key Insight
                </h4>
                <p className="text-sm text-blue-700">
                  {step.insights}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Process Summary */}
        <div className="grid lg:grid-cols-3 gap-8">
          <div className="bg-gradient-to-br from-blue-600 to-blue-700 text-white rounded-3xl p-8">
            <h4 className="text-xl font-bold mb-4">
              Clinical Research
            </h4>
            <p className="mb-4 opacity-90">
              Extensive research with medical professionals ensured our design meets real clinical needs.
            </p>
            <div className="text-3xl font-bold mb-1">150+</div>
            <div className="text-sm opacity-80">Healthcare Professionals Consulted</div>
          </div>
          
          <div className="bg-gradient-to-br from-green-600 to-green-700 text-white rounded-3xl p-8">
            <h4 className="text-xl font-bold mb-4">
              Compliance Focus
            </h4>
            <p className="mb-4 opacity-90">
              Built from the ground up with HIPAA compliance and medical regulations as core requirements.
            </p>
            <div className="text-3xl font-bold mb-1">100%</div>
            <div className="text-sm opacity-80">Regulatory Compliance</div>
          </div>
          
          <div className="bg-gradient-to-br from-purple-600 to-purple-700 text-white rounded-3xl p-8">
            <h4 className="text-xl font-bold mb-4">
              Clinical Validation
            </h4>
            <p className="mb-4 opacity-90">
              Rigorous testing in clinical environments validated our design decisions and usability.
            </p>
            <div className="text-3xl font-bold mb-1">95%</div>
            <div className="text-sm opacity-80">User Satisfaction Score</div>
          </div>
        </div>
      </div>
    </section>
  );
}