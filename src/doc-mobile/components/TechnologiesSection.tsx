export function TechnologiesSection() {
  const technologies = [
    {
      name: 'React',
      icon: '⚛️'
    },
    {
      name: 'TypeScript',
      icon: '📘'
    },
    {
      name: 'MongoDB',
      icon: '🍃'
    },
    {
      name: 'HTML5',
      icon: '🏗️'
    },
    {
      name: 'CSS3',
      icon: '🎨'
    },
    {
      name: 'Node.js',
      icon: '🟢'
    },
    {
      name: 'AWS',
      icon: '☁️'
    },
    {
      name: 'Docker',
      icon: '🐳'
    },
    {
      name: 'Python',
      icon: '🐍'
    },
    {
      name: 'Figma',
      icon: '🎨'
    },
    {
      name: 'Git',
      icon: '📝'
    },
    {
      name: 'API',
      icon: '🔗'
    }
  ];

  return (
    <section className="py-24 px-6 bg-[#1E1E1E]">
      <div className="max-w-7xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Description */}
          <div>
            <div className="inline-flex items-center gap-2 bg-[#0052FD] px-6 py-3 rounded-full mb-8 shadow-lg">
              <span className="text-base text-white font-medium font-['DM_Sans',_sans-serif]">
                Technology Stack
              </span>
            </div>
            
            <h2 className="text-5xl font-bold mb-8 text-white font-['DM_Sans',_sans-serif]">
              Technologies Used
            </h2>
            
            <p className="text-xl text-[#E7E8E9] leading-relaxed font-['DM_Sans',_sans-serif]">
              DocMobil leverages a modern, scalable technology stack to deliver 
              exceptional healthcare experiences. Our platform combines cutting-edge 
              frontend frameworks with robust backend solutions and cloud infrastructure 
              to ensure reliability, performance, and seamless user interactions across 
              all touchpoints.
            </p>
          </div>
          
          {/* Technology Icons Grid */}
          <div>
            <div className="grid grid-cols-3 sm:grid-cols-4 gap-8">
              {technologies.map((tech, index) => (
                <div 
                  key={index} 
                  className="group bg-white rounded-2xl p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 hover:bg-[#E6EEFF] border border-[#E7E8E9]"
                >
                  <div className="text-center">
                    <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                      {tech.icon}
                    </div>
                    <h4 className="font-medium text-[#1E1E1E] text-sm font-['DM_Sans',_sans-serif]">
                      {tech.name}
                    </h4>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}