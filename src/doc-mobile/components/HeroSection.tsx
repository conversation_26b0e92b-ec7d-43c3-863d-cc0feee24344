import exampleImage from 'figma:asset/c97587ebc13da5117e446db51bba765993881444.png';

export function HeroSection() {
  return (
    <section className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 flex items-center justify-center px-6 py-20">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-2 bg-blue-600 px-4 py-2 rounded-full mb-8 shadow-sm">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
            <span className="text-sm text-white font-medium">
              Healthcare • UI/UX Case Study
            </span>
          </div>
          
          <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold mb-8 tracking-tight text-gray-900">
            DocMobil
          </h1>
          
          <p className="text-xl md:text-2xl max-w-4xl mx-auto mb-12 leading-relaxed text-gray-600">
            A comprehensive healthcare platform connecting patients, doctors, and medical professionals 
            through seamless digital experiences that prioritize care and accessibility.
          </p>
          
          <div className="flex flex-wrap justify-center gap-4 mb-16">
            <div className="bg-blue-600 px-6 py-3 rounded-full shadow-sm">
              <span className="text-white">🏥 Patient Management</span>
            </div>
            <div className="bg-blue-600 px-6 py-3 rounded-full shadow-sm">
              <span className="text-white">💬 Telemedicine</span>
            </div>
            <div className="bg-blue-600 px-6 py-3 rounded-full shadow-sm">
              <span className="text-white">📊 Health Analytics</span>
            </div>
            <div className="bg-blue-600 px-6 py-3 rounded-full shadow-sm">
              <span className="text-white">🔒 HIPAA Compliant</span>
            </div>
          </div>
        </div>
        
        {/* Featured Mockup - Zoomed in without frame */}
        <div className="relative flex justify-center">
          <div className="relative z-20 w-full max-w-5xl">
            <img 
              src={exampleImage} 
              alt="DocMobil Healthcare Platform Interface"
              className="w-full h-auto rounded-2xl shadow-2xl"
            />
          </div>
          
          {/* Floating Elements */}
          <div className="absolute top-20 left-10 w-16 h-16 bg-blue-500 rounded-full opacity-60 animate-bounce flex items-center justify-center">
            <span className="text-white text-xl">🏥</span>
          </div>
          <div className="absolute top-40 right-20 w-12 h-12 bg-green-100 rounded-full opacity-40 animate-pulse flex items-center justify-center">
            <span className="text-green-600">💚</span>
          </div>
          <div className="absolute bottom-40 left-20 w-8 h-8 bg-purple-500 rounded-full opacity-50 flex items-center justify-center">
            <span className="text-white text-xs">📊</span>
          </div>
        </div>
      </div>
    </section>
  );
}