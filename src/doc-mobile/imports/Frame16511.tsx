function Frame16509() {
  return (
    <div className="absolute bg-[rgba(0,82,253,0.1)] h-[189px] left-0 rounded-2xl shadow-[0px_4px_19.4px_0px_rgba(0,0,0,0.16)] top-[351px] w-[600px]">
      <div className="flex flex-col justify-end overflow-clip relative size-full">
        <div className="box-border content-stretch flex flex-col gap-2.5 h-[189px] items-start justify-end px-6 py-5 relative w-[600px]">
          <div
            className="flex flex-col font-['DM_Sans:Medium',_sans-serif] font-medium justify-center leading-[0] relative shrink-0 text-[#000000] text-[32px] text-left text-nowrap"
            style={{ fontVariationSettings: "'opsz' 14" }}
          >
            <p className="block leading-[1.29071] whitespace-pre">#E6EEFF</p>
          </div>
        </div>
      </div>
    </div>
  );
}

function Frame16508() {
  return (
    <div className="absolute bg-[#1e1e1e] h-[189px] left-0 rounded-2xl shadow-[0px_4px_19.4px_0px_rgba(0,0,0,0.16)] top-[269px] w-[600px]">
      <div className="flex flex-col justify-end overflow-clip relative size-full">
        <div className="box-border content-stretch flex flex-col gap-2.5 h-[189px] items-start justify-end px-6 py-5 relative w-[600px]">
          <div
            className="flex flex-col font-['DM_Sans:Medium',_sans-serif] font-medium justify-center leading-[0] relative shrink-0 text-[#ffffff] text-[32px] text-left text-nowrap"
            style={{ fontVariationSettings: "'opsz' 14" }}
          >
            <p className="block leading-[1.29071] whitespace-pre">#1E1E1E</p>
          </div>
        </div>
      </div>
    </div>
  );
}

function Frame16507() {
  return (
    <div className="absolute bg-[#e7e8e9] h-[189px] left-0 rounded-2xl shadow-[0px_4px_19.4px_0px_rgba(0,0,0,0.16)] top-[183px] w-[600px]">
      <div className="flex flex-col justify-end overflow-clip relative size-full">
        <div className="box-border content-stretch flex flex-col gap-2.5 h-[189px] items-start justify-end px-6 py-5 relative w-[600px]">
          <div
            className="flex flex-col font-['DM_Sans:Medium',_sans-serif] font-medium justify-center leading-[0] relative shrink-0 text-[#1e1e1e] text-[32px] text-left text-nowrap"
            style={{ fontVariationSettings: "'opsz' 14" }}
          >
            <p className="block leading-[1.29071] whitespace-pre">#E7E8E9</p>
          </div>
        </div>
      </div>
    </div>
  );
}

function Frame16506() {
  return (
    <div className="absolute bg-[#00e500] h-[189px] left-0 rounded-2xl shadow-[0px_4px_19.4px_0px_rgba(0,0,0,0.16)] top-[94px] w-[600px]">
      <div className="flex flex-col justify-end overflow-clip relative size-full">
        <div className="box-border content-stretch flex flex-col gap-2.5 h-[189px] items-start justify-end px-6 py-5 relative w-[600px]">
          <div
            className="flex flex-col font-['DM_Sans:Medium',_sans-serif] font-medium justify-center leading-[0] relative shrink-0 text-[#ffffff] text-[32px] text-left text-nowrap"
            style={{ fontVariationSettings: "'opsz' 14" }}
          >
            <p className="block leading-[1.29071] whitespace-pre">#00E500</p>
          </div>
        </div>
      </div>
    </div>
  );
}

function Frame16505() {
  return (
    <div className="absolute bg-[#0052fd] h-[189px] left-0 rounded-2xl shadow-[0px_4px_19.4px_0px_rgba(0,0,0,0.16)] top-0 w-[600px]">
      <div className="overflow-clip relative size-full">
        <div className="box-border content-stretch flex flex-col gap-2.5 h-[189px] items-start justify-start px-6 py-5 relative w-[600px]">
          <div
            className="flex flex-col font-['DM_Sans:Medium',_sans-serif] font-medium justify-center leading-[0] relative shrink-0 text-[#ffffff] text-[32px] text-left text-nowrap"
            style={{ fontVariationSettings: "'opsz' 14" }}
          >
            <p className="block leading-[1.29071] whitespace-pre">#0052FD</p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function Frame16511() {
  return (
    <div className="relative size-full">
      <Frame16509 />
      <Frame16508 />
      <Frame16507 />
      <Frame16506 />
      <Frame16505 />
    </div>
  );
}