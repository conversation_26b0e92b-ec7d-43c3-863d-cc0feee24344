@import url('https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap');

@custom-variant dark (&:is(.dark *));

:root {
  --font-size: 14px;
  --background: #ffffff;
  --foreground: #1E1E1E;
  --card: #ffffff;
  --card-foreground: #1E1E1E;
  --popover: #ffffff;
  --popover-foreground: #1E1E1E;
  --primary: #0052FD;
  --primary-foreground: #ffffff;
  --secondary: #E7E8E9;
  --secondary-foreground: #1E1E1E;
  --success: #00E500;
  --success-foreground: #ffffff;
  --muted: #E6EEFF;
  --muted-foreground: #1E1E1E;
  --accent: #E6EEFF;
  --accent-foreground: #1E1E1E;
  --destructive: #d4183d;
  --destructive-foreground: #ffffff;
  --border: rgba(0, 0, 0, 0.1);
  --input: transparent;
  --input-background: #E6EEFF;
  --switch-background: #E7E8E9;
  --font-weight-medium: 500;
  --font-weight-normal: 400;
  --ring: #0052FD;
  --chart-1: #0052FD;
  --chart-2: #00E500;
  --chart-3: #E7E8E9;
  --chart-4: #1E1E1E;
  --chart-5: #E6EEFF;
  --radius: 0.625rem;
  --sidebar: #ffffff;
  --sidebar-foreground: #1E1E1E;
  --sidebar-primary: #0052FD;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #E6EEFF;
  --sidebar-accent-foreground: #1E1E1E;
  --sidebar-border: #E7E8E9;
  --sidebar-ring: #0052FD;
}

.dark {
  --background: #1E1E1E;
  --foreground: #ffffff;
  --card: #1E1E1E;
  --card-foreground: #ffffff;
  --popover: #1E1E1E;
  --popover-foreground: #ffffff;
  --primary: #0052FD;
  --primary-foreground: #ffffff;
  --secondary: #E7E8E9;
  --secondary-foreground: #1E1E1E;
  --success: #00E500;
  --success-foreground: #ffffff;
  --muted: #E6EEFF;
  --muted-foreground: #1E1E1E;
  --accent: #E6EEFF;
  --accent-foreground: #1E1E1E;
  --destructive: #d4183d;
  --destructive-foreground: #ffffff;
  --border: rgba(255, 255, 255, 0.1);
  --input: rgba(255, 255, 255, 0.1);
  --ring: #0052FD;
  --font-weight-medium: 500;
  --font-weight-normal: 400;
  --chart-1: #0052FD;
  --chart-2: #00E500;
  --chart-3: #E7E8E9;
  --chart-4: #ffffff;
  --chart-5: #E6EEFF;
  --sidebar: #1E1E1E;
  --sidebar-foreground: #ffffff;
  --sidebar-primary: #0052FD;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: rgba(0, 82, 253, 0.1);
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: rgba(255, 255, 255, 0.1);
  --sidebar-ring: #0052FD;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-input-background: var(--input-background);
  --color-switch-background: var(--switch-background);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'DM Sans', sans-serif;
  }
}

/**
 * Base typography using DM Sans. This is not applied to elements which have an ancestor with a Tailwind text class.
 */
@layer base {
  :where(:not(:has([class*=" text-"]), :not(:has([class^="text-"])))) {
    h1 {
      font-family: 'DM Sans', sans-serif;
      font-size: var(--text-2xl);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    h2 {
      font-family: 'DM Sans', sans-serif;
      font-size: var(--text-xl);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    h3 {
      font-family: 'DM Sans', sans-serif;
      font-size: var(--text-lg);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    h4 {
      font-family: 'DM Sans', sans-serif;
      font-size: var(--text-base);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    p {
      font-family: 'DM Sans', sans-serif;
      font-size: var(--text-base);
      font-weight: var(--font-weight-normal);
      line-height: 1.5;
    }

    label {
      font-family: 'DM Sans', sans-serif;
      font-size: var(--text-base);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    button {
      font-family: 'DM Sans', sans-serif;
      font-size: var(--text-base);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    input {
      font-family: 'DM Sans', sans-serif;
      font-size: var(--text-base);
      font-weight: var(--font-weight-normal);
      line-height: 1.5;
    }
  }
}

html {
  font-size: var(--font-size);
  font-family: 'DM Sans', sans-serif;
}