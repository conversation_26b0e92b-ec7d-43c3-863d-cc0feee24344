import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Header from "./components/Header";
import HeroSection from "./components/HeroSection";
import StatsSection from "./components/StatsSection";
import ExpertiseSection from "./components/ExpertiseSection";
import AdvantageSection from "./components/AdvantageSection";
import TestimonialsSection from "./components/TestimonialsSection";
import ContactSection from "./components/ContactSection";
import Footer from "./components/Footer";
import AIServicesPage from "./components/AIServicesPage";
import DevelopmentServicesPage from "./components/DevelopmentServicesPage";
import CloudServicesPage from "./components/CloudServicesPage";
import DigitalStrategyPage from "./components/DigitalStrategyPage";
import AboutUsPage from "./components/AboutUsPage";
import ContactPage from "./components/ContactPage";
import PortfolioPage from "./components/PortfolioPage";

// Home page component
function HomePage() {
  return (
    <main className="pt-20">
      {/* Hero Section */}
      <HeroSection />

      {/* Stats Section */}
      <StatsSection />

      {/* Our Expertise */}
      <ExpertiseSection />

      {/* Your Unfair Advantage */}
      <AdvantageSection />

      {/* What Our Clients Say */}
      <TestimonialsSection />

      {/* Let's Talk (Contact) */}
      <ContactSection />
    </main>
  );
}

// Layout component with Header and Footer
function Layout({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen bg-background text-foreground dark">
      <Header />
      {children}
      <Footer />
    </div>
  );
}

export default function App() {
  return (
    <Router>
      <Routes>
        <Route
          path="/"
          element={
            <Layout>
              <HomePage />
            </Layout>
          }
        />
        <Route
          path="/ai-services"
          element={
            <Layout>
              <AIServicesPage />
            </Layout>
          }
        />
        <Route
          path="/development-services"
          element={
            <Layout>
              <DevelopmentServicesPage />
            </Layout>
          }
        />
        <Route
          path="/cloud-services"
          element={
            <Layout>
              <CloudServicesPage />
            </Layout>
          }
        />
        <Route
          path="/digital-strategy"
          element={
            <Layout>
              <DigitalStrategyPage />
            </Layout>
          }
        />
        <Route
          path="/about-us"
          element={
            <Layout>
              <AboutUsPage />
            </Layout>
          }
        />
        <Route
          path="/contact"
          element={
            <Layout>
              <ContactPage />
            </Layout>
          }
        />
        <Route
          path="/portfolio"
          element={
            <Layout>
              <PortfolioPage />
            </Layout>
          }
        />
      </Routes>
    </Router>
  );
}
