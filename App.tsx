import { useState, useEffect } from "react";
import Header from "./components/Header";
import HeroSection from "./components/HeroSection";
import StatsSection from "./components/StatsSection";
import ExpertiseSection from "./components/ExpertiseSection";
import AdvantageSection from "./components/AdvantageSection";
import TestimonialsSection from "./components/TestimonialsSection";
import ContactSection from "./components/ContactSection";
import Footer from "./components/Footer";
import AIServicesPage from "./components/AIServicesPage";
import DevelopmentServicesPage from "./components/DevelopmentServicesPage";
import CloudServicesPage from "./components/CloudServicesPage";
import DigitalStrategyPage from "./components/DigitalStrategyPage";
import AboutUsPage from "./components/AboutUsPage";
import ContactPage from "./components/ContactPage";
import PortfolioPage from "./components/PortfolioPage";

export default function App() {
  const [currentPage, setCurrentPage] = useState("home");

  useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash.replace("#", "");
      if (hash === "ai-services") {
        setCurrentPage("ai-services");
      } else if (hash === "development-services") {
        setCurrentPage("development-services");
      } else if (hash === "cloud-services") {
        setCurrentPage("cloud-services");
      } else if (hash === "digital-strategy") {
        setCurrentPage("digital-strategy");
      } else if (hash === "about-us") {
        setCurrentPage("about-us");
      } else if (hash === "contact") {
        setCurrentPage("contact");
      } else if (hash === "portfolio") {
        setCurrentPage("portfolio");
      } else {
        setCurrentPage("home");
      }
    };

    // Check initial hash
    handleHashChange();

    // Listen for hash changes
    window.addEventListener("hashchange", handleHashChange);

    return () => {
      window.removeEventListener("hashchange", handleHashChange);
    };
  }, []);

  if (currentPage === "ai-services") {
    return (
      <div className="min-h-screen bg-background text-foreground dark">
        <Header />
        <AIServicesPage />
        <Footer />
      </div>
    );
  }

  if (currentPage === "development-services") {
    return (
      <div className="min-h-screen bg-background text-foreground dark">
        <Header />
        <DevelopmentServicesPage />
        <Footer />
      </div>
    );
  }

  if (currentPage === "cloud-services") {
    return (
      <div className="min-h-screen bg-background text-foreground dark">
        <Header />
        <CloudServicesPage />
        <Footer />
      </div>
    );
  }

  if (currentPage === "digital-strategy") {
    return (
      <div className="min-h-screen bg-background text-foreground dark">
        <Header />
        <DigitalStrategyPage />
        <Footer />
      </div>
    );
  }

  if (currentPage === "about-us") {
    return (
      <div className="min-h-screen bg-background text-foreground dark">
        <Header />
        <AboutUsPage />
        <Footer />
      </div>
    );
  }

  if (currentPage === "contact") {
    return (
      <div className="min-h-screen bg-background text-foreground dark">
        <Header />
        <ContactPage />
        <Footer />
      </div>
    );
  }

  if (currentPage === "portfolio") {
    return (
      <div className="min-h-screen bg-background text-foreground dark">
        <Header />
        <PortfolioPage />
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background text-foreground dark">
      {/* Header */}
      <Header />

      {/* Main Content */}
      <main className="pt-20">
        {/* Hero Section */}
        <HeroSection />

        {/* Stats Section */}
        <StatsSection />

        {/* Our Expertise */}
        <ExpertiseSection />

        {/* Your Unfair Advantage */}
        <AdvantageSection />

        {/* What Our Clients Say */}
        <TestimonialsSection />

        {/* Let's Talk (Contact) */}
        <ContactSection />
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
}
