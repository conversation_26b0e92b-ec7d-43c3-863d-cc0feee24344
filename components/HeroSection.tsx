import React, { useEffect, useState } from "react";
import { But<PERSON> } from "./ui/button";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Rocket,
  Zap,
  Users,
  Sparkles,
  <PERSON>,
  Clock,
  Trophy,
  Target,
} from "lucide-react";

const HeroSection = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener("mousemove", handleMouseMove);
    return () => window.removeEventListener("mousemove", handleMouseMove);
  }, []);

  const scrollToContact = () => {
    const contactSection = document.getElementById("contact");
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <section className="relative min-h-[85vh] flex items-center justify-center gradient-hero overflow-hidden">
      {/* Dynamic Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Background Image */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-60"
          style={{
            backgroundImage:
              "url(https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2525&q=80)",
          }}
        ></div>

        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/75 via-black/50 to-black/75"></div>

        {/* Animated Grid */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(84,37,176,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(84,37,176,0.03)_1px,transparent_1px)] bg-[size:100px_100px] animate-gradient"></div>

        {/* Floating Orbs */}
        <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-gradient-to-r from-primary/20 to-purple-500/10 rounded-full blur-3xl animate-float"></div>
        <div
          className="absolute top-1/4 right-1/3 w-[500px] h-[500px] bg-gradient-to-l from-purple-500/15 to-primary/5 rounded-full blur-2xl animate-float"
          style={{ animationDelay: "2s" }}
        ></div>
        <div
          className="absolute top-1/2 left-1/2 w-48 h-48 bg-gradient-to-r from-primary/25 to-violet-500/10 rounded-full blur-xl animate-float"
          style={{ animationDelay: "4s" }}
        ></div>

        {/* Particle System */}
        <div className="particles">
          {[...Array(50)].map((_, i) => (
            <div
              key={i}
              className="particle"
              style={{
                left: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 15}s`,
                animationDuration: `${15 + Math.random() * 10}s`,
              }}
            />
          ))}
        </div>

        {/* Mouse Following Gradient */}
        <div
          className="absolute w-96 h-96 bg-gradient-to-r from-primary/10 to-transparent rounded-full blur-3xl opacity-30 transition-all duration-1000 ease-out pointer-events-none"
          style={{
            left: mousePosition.x - 192,
            top: mousePosition.y - 192,
          }}
        />
      </div>

      <div className="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8 text-center py-12 sm:py-16">
        <div className="max-w-5xl mx-auto">
          {/* Badge */}
          <div className="inline-flex items-center gap-2 bg-gradient-card rounded-full px-6 py-3 mb-6 sm:mb-8 animate-fade-in-down">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-sm font-display font-medium text-gray-300">
                Trusted by 50+ Global Companies
              </span>
            </div>
          </div>

          {/* Main Heading */}
          <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-display font-black text-white mb-4 sm:mb-6 animate-fade-in-up text-balance text-[40px]">
            Turn Your Vision Into
            <span className="block gradient-text-primary animate-gradient">
              Digital Reality
            </span>
            <span className="block text-3xl sm:text-4xl lg:text-5xl xl:text-6xl mt-2 text-gray-300 text-[36px]">
              In Record Time
            </span>
          </h1>

          {/* Tagline */}
          <p
            className="text-lg sm:text-xl lg:text-2xl font-body font-medium text-gray-300 mb-6 sm:mb-8 max-w-4xl mx-auto animate-fade-in-up text-pretty"
            style={{ animationDelay: "0.2s" }}
          >
            We're the AI-powered digital engineering partner that helps
            ambitious businesses build, scale, and dominate their markets with
            cutting-edge technology solutions.
          </p>

          {/* Trust Indicators */}
          <div
            className="flex flex-wrap justify-center items-center gap-6 mb-8 sm:mb-12 animate-fade-in-up"
            style={{ animationDelay: "0.3s" }}
          >
            <div className="flex items-center gap-2 text-gray-400 text-sm">
              <Shield className="w-4 h-4 text-green-400" />
              <span>Enterprise Grade Security</span>
            </div>
            <div className="flex items-center gap-2 text-gray-400 text-sm">
              <Clock className="w-4 h-4 text-blue-400" />
              <span>30% Faster Delivery</span>
            </div>
            <div className="flex items-center gap-2 text-gray-400 text-sm">
              <Trophy className="w-4 h-4 text-yellow-400" />
              <span>99% Client Satisfaction</span>
            </div>
          </div>

          {/* CTA Section */}
          <div
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12 sm:mb-16 animate-fade-in-up"
            style={{ animationDelay: "0.6s" }}
          >
            <button onClick={scrollToContact} className="btn-modern group">
              Start Your Project Today
              <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
            </button>

            <div className="flex items-center gap-2 text-sm text-gray-400">
              <div className="flex -space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full border-2 border-gray-800"></div>
                <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-full border-2 border-gray-800"></div>
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full border-2 border-gray-800"></div>
                <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-full border-2 border-gray-800 flex items-center justify-center">
                  <span className="text-xs font-semibold text-white">+</span>
                </div>
              </div>
              <span>Join 50+ successful companies</span>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-6 sm:bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center backdrop-blur-sm">
          <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
