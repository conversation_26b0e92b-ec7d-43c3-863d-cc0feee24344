import React, { useEffect, useRef } from 'react';
import { Brain, Code, Target, Server, Lightbulb, Shield, ArrowUpRight } from 'lucide-react';

const ExpertiseSection = () => {
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const cards = entry.target.querySelectorAll('.expertise-card');
            cards.forEach((card, index) => {
              setTimeout(() => {
                card.classList.add('revealed');
              }, index * 100);
            });
          }
        });
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const expertiseItems = [
    {
      icon: Brain,
      title: 'AI & Machine Learning',
      description: 'Advanced AI solutions and intelligent data analytics to drive business transformation.',
      gradient: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 50%, #6d28d9 100%)',
      hoverBg: 'linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(124, 58, 237, 0.05) 100%)',
      tagBg: 'rgba(139, 92, 246, 0.15)',
      borderColor: 'rgba(139, 92, 246, 0.3)',
      features: ['Machine Learning', 'Data Analytics', 'AI Automation']
    },
    {
      icon: Code,
      title: 'Software Development',
      description: 'Full-stack development with comprehensive testing frameworks for scalable applications.',
      gradient: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 50%, #0e7490 100%)',
      hoverBg: 'linear-gradient(135deg, rgba(6, 182, 212, 0.1) 0%, rgba(8, 145, 178, 0.05) 100%)',
      tagBg: 'rgba(6, 182, 212, 0.15)',
      borderColor: 'rgba(6, 182, 212, 0.3)',
      features: ['Full-Stack Dev', 'Quality Assurance', 'Code Review']
    },
    {
      icon: Target,
      title: 'Business Consulting',
      description: 'Strategic technology guidance aligning digital solutions with business objectives.',
      gradient: 'linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%)',
      hoverBg: 'linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.05) 100%)',
      tagBg: 'rgba(16, 185, 129, 0.15)',
      borderColor: 'rgba(16, 185, 129, 0.3)',
      features: ['Digital Strategy', 'Business Analysis', 'Market Research']
    },
    {
      icon: Server,
      title: 'Enterprise Integration',
      description: 'Seamless integration of enterprise systems optimizing workflows and efficiency.',
      gradient: 'linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #b45309 100%)',
      hoverBg: 'linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.05) 100%)',
      tagBg: 'rgba(245, 158, 11, 0.15)',
      borderColor: 'rgba(245, 158, 11, 0.3)',
      features: ['System Integration', 'Platform Migration', 'Workflow Optimization']
    },
    {
      icon: Lightbulb,
      title: 'Product Innovation',
      description: 'From concept to market launch, bringing innovative digital products to life.',
      gradient: 'linear-gradient(135deg, #ec4899 0%, #db2777 50%, #be185d 100%)',
      hoverBg: 'linear-gradient(135deg, rgba(236, 72, 153, 0.1) 0%, rgba(219, 39, 119, 0.05) 100%)',
      tagBg: 'rgba(236, 72, 153, 0.15)',
      borderColor: 'rgba(236, 72, 153, 0.3)',
      features: ['Product Design', 'MVP Development', 'Market Launch']
    },
    {
      icon: Shield,
      title: 'Cloud & Security',
      description: 'Secure cloud infrastructure and comprehensive cybersecurity solutions.',
      gradient: 'linear-gradient(135deg, #6366f1 0%, #4f46e5 50%, #4338ca 100%)',
      hoverBg: 'linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(79, 70, 229, 0.05) 100%)',
      tagBg: 'rgba(99, 102, 241, 0.15)',
      borderColor: 'rgba(99, 102, 241, 0.3)',
      features: ['Cloud Migration', 'Security Audit', 'Infrastructure']
    }
  ];

  return (
    <section ref={sectionRef} className="py-16 lg:py-20 gradient-section relative overflow-hidden">
      {/* Subtle Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-10 right-10 w-32 h-32 bg-primary/3 rounded-full blur-2xl animate-float"></div>
        <div className="absolute bottom-10 left-10 w-24 h-24 bg-purple-500/3 rounded-full blur-xl animate-float" style={{ animationDelay: '3s' }}></div>
      </div>

      <div className="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8">
        {/* Compact Section Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 bg-gradient-card rounded-full px-4 py-2 mb-4">
            <div className="w-1.5 h-1.5 bg-primary rounded-full animate-pulse"></div>
            <span className="text-xs font-display font-medium text-gray-400 uppercase tracking-wide">Our Core Competencies</span>
          </div>
          
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-display font-black text-white mb-3 text-balance">
            Our <span className="gradient-text-primary">Expertise</span>
          </h2>
          
          <p className="text-gray-400 max-w-2xl mx-auto font-body leading-relaxed text-pretty">
            We deliver world-class digital solutions across multiple domains, empowering businesses to thrive in the digital age.
          </p>
        </div>

        {/* Compact Expertise Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 lg:gap-5">
          {expertiseItems.map((item, index) => {
            const Icon = item.icon;
            return (
              <div
                key={index}
                className="expertise-card scroll-reveal group relative glass-strong rounded-2xl p-5 hover-lift hover-glow transition-all duration-500 border border-white/10"
                style={{
                  '--hover-bg': item.hoverBg,
                } as React.CSSProperties}
              >
                {/* Gradient Background */}
                <div 
                  className="absolute inset-0 opacity-0 group-hover:opacity-100 rounded-2xl transition-opacity duration-500"
                  style={{ background: item.hoverBg }}
                ></div>
                
                {/* Content */}
                <div className="relative z-10">
                  {/* Icon & Title Row */}
                  <div className="flex items-start justify-between mb-3">
                    <div 
                      className="w-12 h-12 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 flex-shrink-0"
                      style={{ background: item.gradient }}
                    >
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    <button className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 p-1.5 rounded-lg hover:bg-white/10">
                      <ArrowUpRight className="w-4 h-4 text-gray-400 hover:text-primary" />
                    </button>
                  </div>

                  {/* Title */}
                  <h3 className="font-display font-bold text-white mb-2 group-hover:text-primary transition-colors text-lg">
                    {item.title}
                  </h3>

                  {/* Description */}
                  <p className="text-gray-400 font-body leading-relaxed mb-3 text-sm text-pretty">
                    {item.description}
                  </p>

                  {/* Compact Features */}
                  <div className="flex flex-wrap gap-1">
                    {item.features.map((feature, featureIndex) => (
                      <span 
                        key={featureIndex} 
                        className="text-xs px-2 py-1 rounded-md text-gray-300 border"
                        style={{ 
                          backgroundColor: item.tagBg,
                          borderColor: item.borderColor
                        }}
                      >
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Subtle Shimmer Effect */}
                <div className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 animate-shimmer"></div>
              </div>
            );
          })}
        </div>

        {/* Compact CTA Section */}
        <div className="text-center mt-12">
          <div className="glass rounded-2xl p-6 border border-white/10 max-w-2xl mx-auto">
            <h3 className="font-display font-bold text-white mb-2 text-xl">
              Ready to Transform Your Business?
            </h3>
            <p className="text-gray-400 font-body mb-4 text-sm text-pretty">
              Let's discuss how our expertise can accelerate your digital transformation.
            </p>
            
            <div className="flex flex-col sm:flex-row items-center justify-center gap-3">
              <button 
                onClick={() => {
                  const contactSection = document.getElementById('contact');
                  if (contactSection) {
                    contactSection.scrollIntoView({ behavior: 'smooth' });
                  }
                }}
                className="btn-modern text-sm px-6 py-2.5"
              >
                Start Your Project
                <ArrowUpRight className="ml-2 w-3.5 h-3.5" />
              </button>
              
              <button className="flex items-center gap-2 px-4 py-2.5 rounded-xl border border-white/20 text-white hover:bg-white/10 transition-all duration-300 font-display font-medium text-sm">
                View Case Studies
                <ArrowUpRight className="w-3.5 h-3.5" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ExpertiseSection;