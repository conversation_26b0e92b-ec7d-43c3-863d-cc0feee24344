const StatsSection = () => {
  return (
    <section className="py-16 lg:py-20 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-b from-background via-gray-950/30 to-background">
        {/* Subtle accent */}
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[200px] bg-primary/3 rounded-full blur-3xl"></div>
      </div>

      <div className="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-5xl mx-auto">
          {/* Stats */}
          <div className="pt-12 border-t border-gray-800/50 animate-fade-in-up">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 text-center">
              {[
                { number: "500+", label: "Projects Delivered", suffix: "" },
                { number: "50", label: "Global Clients", suffix: "+" },
                { number: "15", label: "Years Experience", suffix: "+" },
              ].map((stat, index) => (
                <div key={index} className="group">
                  <div className="text-3xl sm:text-4xl font-display font-black text-primary mb-2 group-hover:scale-110 transition-transform duration-300">
                    {stat.number}
                    <span className="text-2xl">{stat.suffix}</span>
                  </div>
                  <div className="text-gray-400 font-body text-sm">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default StatsSection;
