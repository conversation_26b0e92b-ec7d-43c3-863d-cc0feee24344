import React, { useState } from "react";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Textarea } from "./ui/textarea";
import { Label } from "./ui/label";
import { Send, Phone, Mail, MapPin, Calendar } from "lucide-react";

const ContactSection = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    company: "",
    message: "",
  });

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission here
    console.log("Form submitted:", formData);
    // Reset form
    setFormData({
      name: "",
      email: "",
      company: "",
      message: "",
    });
  };

  return (
    <section id="contact" className="py-20 lg:py-32 gradient-section">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-montserrat font-bold text-white mb-6">
            Let's <span className="text-primary">Talk</span>
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto font-open-sans">
            Ready to transform your business with cutting-edge technology? Let's
            discuss your project and explore how we can help you achieve your
            goals.
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Column - Contact Info & Illustration */}
            <div className="space-y-8">
              {/* Illustration/Image */}
              <div className="relative">
                <div className="bg-[#151515] backdrop-blur-sm rounded-2xl p-8 border border-border">
                  <div className="w-full h-64 bg-[#21172e] from-primary/20 to-purple-500/10 rounded-xl flex items-center justify-center">
                    <div className="text-center">
                      <div className="w-20 h-20 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                        <Phone className="w-10 h-10 text-white" />
                      </div>
                      <h3 className="text-2xl font-montserrat font-bold text-white mb-2">
                        Let's Connect
                      </h3>
                      <p className="text-gray-400 font-open-sans">
                        Start your digital transformation journey today
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center">
                    <Mail className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-montserrat font-semibold text-white">
                      Email Us
                    </h4>
                    <p className="text-gray-400 font-open-sans">
                      <EMAIL>
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center">
                    <Phone className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-montserrat font-semibold text-white">
                      Call Us
                    </h4>
                    <p className="text-gray-400 font-open-sans">
                      +****************
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center">
                    <MapPin className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-montserrat font-semibold text-white">
                      Visit Us
                    </h4>
                    <p className="text-gray-400 font-open-sans">
                      Global offices in US, UK, and Asia
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center">
                    <Calendar className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-montserrat font-semibold text-white">
                      Schedule a Call
                    </h4>
                    <p className="text-gray-400 font-open-sans">
                      Book a free consultation
                    </p>
                  </div>
                </div>
              </div>

              {/* Quick Response Promise */}
              <div className="bg-[#141121] border border-white/10 rounded-xl p-6">
                <h4 className="font-montserrat font-bold text-white mb-2">
                  Quick Response Guarantee
                </h4>
                <p className="text-gray-400 font-open-sans text-sm">
                  We respond to all inquiries within 24 hours. For urgent
                  matters, call us directly for immediate assistance.
                </p>
              </div>
            </div>

            {/* Right Column - Contact Form */}
            <div className="bg-[#151515] backdrop-blur-sm rounded-2xl p-8 border border-border">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <Label
                    htmlFor="name"
                    className="text-white font-montserrat font-semibold"
                  >
                    Full Name*
                  </Label>
                  <Input
                    id="name"
                    name="name"
                    type="text"
                    required
                    value={formData.name}
                    onChange={handleInputChange}
                    className="mt-2 bg-white/5 border-white/20 text-white placeholder-gray-400 focus:border-primary focus:ring-primary"
                    placeholder="Enter your full name"
                  />
                </div>

                <div>
                  <Label
                    htmlFor="email"
                    className="text-white font-montserrat font-semibold"
                  >
                    Email Address*
                  </Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    required
                    value={formData.email}
                    onChange={handleInputChange}
                    className="mt-2 bg-white/5 border-white/20 text-white placeholder-gray-400 focus:border-primary focus:ring-primary"
                    placeholder="Enter your email address"
                  />
                </div>

                <div>
                  <Label
                    htmlFor="company"
                    className="text-white font-montserrat font-semibold"
                  >
                    Company Name
                  </Label>
                  <Input
                    id="company"
                    name="company"
                    type="text"
                    value={formData.company}
                    onChange={handleInputChange}
                    className="mt-2 bg-white/5 border-white/20 text-white placeholder-gray-400 focus:border-primary focus:ring-primary"
                    placeholder="Enter your company name"
                  />
                </div>

                <div>
                  <Label
                    htmlFor="message"
                    className="text-white font-montserrat font-semibold"
                  >
                    Project Details*
                  </Label>
                  <Textarea
                    id="message"
                    name="message"
                    required
                    rows={6}
                    value={formData.message}
                    onChange={handleInputChange}
                    className="mt-2 bg-white/5 border-white/20 text-white placeholder-gray-400 focus:border-primary focus:ring-primary resize-none"
                    placeholder="Tell us about your project, goals, and requirements..."
                  />
                </div>

                <Button
                  type="submit"
                  className="w-full bg-primary hover:bg-primary/90 text-white font-montserrat font-bold text-lg py-3 transition-all duration-300 hover-glow group"
                >
                  Send Message
                  <Send className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </Button>

                <div className="text-center">
                  <p className="text-gray-400 font-open-sans text-sm">
                    By submitting this form, you agree to our privacy policy and
                    terms of service.
                  </p>
                </div>
              </form>

              {/* Alternative Contact Methods */}
              <div className="mt-8 pt-6 border-t border-border">
                <h4 className="font-montserrat font-semibold text-white mb-4 text-center">
                  Prefer a Different Way?
                </h4>
                <div className="flex flex-col sm:flex-row gap-3">
                  <Button
                    variant="outline"
                    className="flex-1 bg-[#1d1d1d] border-primary/30 text-primary hover:bg-primary hover:text-white transition-all duration-300"
                  >
                    <Calendar className="w-4 h-4 mr-2" />
                    Book a Call
                  </Button>
                  <Button
                    variant="outline"
                    className="flex-1 bg-[#1d1d1d] border-primary/30 text-primary hover:bg-primary hover:text-white transition-all duration-300"
                  >
                    <Phone className="w-4 h-4 mr-2" />
                    Call Now
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
