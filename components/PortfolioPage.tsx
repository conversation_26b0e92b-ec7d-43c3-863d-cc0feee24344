import { useEffect, useState, useMemo } from "react";
import { But<PERSON> } from "./ui/button";
import { Card, CardContent } from "./ui/card";
import { Badge } from "./ui/badge";
import {
  ArrowRight,
  Award,
  Users,
  Brain,
  Globe,
  Smartphone,
  Cloud,
  BarChart3,
  Shield,
  Zap,
  Database,
  Code,
  Target,
  Briefcase,
  Star,
} from "lucide-react";

const PortfolioPage = () => {
  const [filter, setFilter] = useState("all");
  const [visibleProjects, setVisibleProjects] = useState(8);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("revealed");
          }
        });
      },
      { threshold: 0.1 }
    );

    const elements = document.querySelectorAll(".scroll-reveal");
    elements.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  // Reset visible projects when filter changes
  useEffect(() => {
    setVisibleProjects(8);
  }, [filter]);

  const handleContactClick = () => {
    window.location.hash = "contact";
  };

  const portfolioProjects = [
    {
      id: 1,
      title: "AI-Powered Healthcare Analytics Platform",
      description:
        "Developed a comprehensive healthcare analytics platform using machine learning to predict patient outcomes and optimize treatment plans. Increased diagnostic accuracy by 35% across 50+ hospitals.",
      category: "ai",
      image:
        "https://images.unsplash.com/photo-**********-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      client: "MedTech Solutions",
      year: "2024",
      technologies: ["Python", "TensorFlow", "React", "AWS", "PostgreSQL"],
      results: "35% accuracy improvement",
      icon: Brain,
    },
    {
      id: 2,
      title: "E-Commerce Platform Transformation",
      description:
        "Complete digital transformation of a traditional retail business into a modern e-commerce platform. Implemented AI-driven recommendations and automated inventory management.",
      category: "web",
      image:
        "https://images.unsplash.com/photo-**********-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      client: "RetailMax Corporation",
      year: "2024",
      technologies: ["React", "Node.js", "MongoDB", "Stripe", "AWS"],
      results: "150% sales increase",
      icon: Globe,
    },
    {
      id: 3,
      title: "FinTech Mobile Banking Application",
      description:
        "Built a secure, user-friendly mobile banking application with biometric authentication, real-time transactions, and AI-powered fraud detection for a leading financial institution.",
      category: "mobile",
      image:
        "https://images.unsplash.com/photo-**********-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      client: "SecureBank Ltd.",
      year: "2024",
      technologies: ["React Native", "Node.js", "Blockchain", "Biometrics"],
      results: "100K+ active users",
      icon: Smartphone,
    },
    {
      id: 4,
      title: "Enterprise Cloud Migration & Infrastructure",
      description:
        "Seamlessly migrated legacy enterprise systems to cloud infrastructure, implementing DevOps practices and microservices architecture. Reduced operational costs by 40%.",
      category: "cloud",
      image:
        "https://images.unsplash.com/photo-*************-43490279c0fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      client: "TechCorp Global",
      year: "2023",
      technologies: ["AWS", "Docker", "Kubernetes", "Terraform", "CI/CD"],
      results: "40% cost reduction",
      icon: Cloud,
    },
    {
      id: 5,
      title: "Real-Time Business Intelligence Dashboard",
      description:
        "Created an advanced business intelligence platform with real-time data visualization, predictive analytics, and automated reporting capabilities for data-driven decision making.",
      category: "ai",
      image:
        "https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      client: "DataCorp Analytics",
      year: "2023",
      technologies: [
        "Python",
        "Tableau",
        "Apache Spark",
        "PostgreSQL",
        "Redis",
      ],
      results: "60% faster insights",
      icon: BarChart3,
    },
    {
      id: 6,
      title: "Blockchain Supply Chain Solution",
      description:
        "Developed a transparent blockchain-based supply chain tracking system enabling end-to-end product traceability and authenticity verification across global networks.",
      category: "blockchain",
      image:
        "https://images.unsplash.com/photo-1639762681485-074b7f938ba0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      client: "LogiChain International",
      year: "2023",
      technologies: ["Ethereum", "Solidity", "Web3.js", "IPFS", "React"],
      results: "95% traceability improvement",
      icon: Shield,
    },
    {
      id: 7,
      title: "AI-Powered Educational Platform",
      description:
        "Built a comprehensive learning management system with AI-driven personalized learning paths, virtual classrooms, and automated assessment tools for modern education.",
      category: "web",
      image:
        "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      client: "EduTech Institute",
      year: "2023",
      technologies: ["Vue.js", "Laravel", "MySQL", "WebRTC", "AWS"],
      results: "50K+ students served",
      icon: Users,
    },
    {
      id: 8,
      title: "IoT Fleet Management System",
      description:
        "Developed a smart IoT-based fleet management system with real-time tracking, predictive maintenance, and route optimization for transportation companies.",
      category: "iot",
      image:
        "https://images.unsplash.com/photo-1586953208448-b95a79798f07?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      client: "FleetOps Solutions",
      year: "2023",
      technologies: ["IoT Sensors", "Node.js", "MongoDB", "React", "AWS IoT"],
      results: "25% fuel savings",
      icon: Target,
    },
    {
      id: 9,
      title: "AI Content Management & Automation",
      description:
        "Created an intelligent content management system with automated categorization, SEO optimization, and multi-language content generation using advanced NLP techniques.",
      category: "ai",
      image:
        "https://images.unsplash.com/photo-1555949963-aa79dcee981c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      client: "ContentHub Media",
      year: "2023",
      technologies: ["Python", "NLP", "TensorFlow", "React", "Docker"],
      results: "80% automation achieved",
      icon: Zap,
    },
    {
      id: 10,
      title: "Cybersecurity Threat Detection Platform",
      description:
        "Built an AI-powered cybersecurity platform that detects and responds to threats in real-time, protecting enterprise networks from advanced persistent threats.",
      category: "security",
      image:
        "https://images.unsplash.com/photo-1550751827-4bd374c3f58b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      client: "CyberDefense Corp",
      year: "2023",
      technologies: [
        "Python",
        "Machine Learning",
        "Elasticsearch",
        "React",
        "Docker",
      ],
      results: "99.9% threat detection",
      icon: Shield,
    },
    {
      id: 11,
      title: "Smart City Traffic Management",
      description:
        "Implemented an intelligent traffic management system using IoT sensors and AI algorithms to optimize traffic flow and reduce congestion in urban areas.",
      category: "iot",
      image:
        "https://images.unsplash.com/photo-1449824913935-59a10b8d2000?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      client: "SmartCity Solutions",
      year: "2022",
      technologies: ["IoT", "Python", "TensorFlow", "MongoDB", "React"],
      results: "30% traffic improvement",
      icon: Target,
    },
    {
      id: 12,
      title: "Enterprise Resource Planning System",
      description:
        "Developed a comprehensive ERP system with integrated modules for finance, HR, inventory, and customer management, streamlining business operations.",
      category: "web",
      image:
        "https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      client: "Global Enterprises Inc.",
      year: "2022",
      technologies: ["Angular", "Spring Boot", "PostgreSQL", "Microservices"],
      results: "45% efficiency gain",
      icon: Database,
    },
  ];

  const categories = useMemo(
    () => [
      { key: "all", label: "All Projects", count: portfolioProjects.length },
      {
        key: "ai",
        label: "AI & ML",
        count: portfolioProjects.filter((p) => p.category === "ai").length,
      },
      {
        key: "web",
        label: "Web Development",
        count: portfolioProjects.filter((p) => p.category === "web").length,
      },
      {
        key: "mobile",
        label: "Mobile Apps",
        count: portfolioProjects.filter((p) => p.category === "mobile").length,
      },
      {
        key: "cloud",
        label: "Cloud Solutions",
        count: portfolioProjects.filter((p) => p.category === "cloud").length,
      },
      {
        key: "blockchain",
        label: "Blockchain",
        count: portfolioProjects.filter((p) => p.category === "blockchain")
          .length,
      },
      {
        key: "iot",
        label: "IoT Solutions",
        count: portfolioProjects.filter((p) => p.category === "iot").length,
      },
      {
        key: "security",
        label: "Security",
        count: portfolioProjects.filter((p) => p.category === "security")
          .length,
      },
    ],
    []
  );

  const filteredProjects = useMemo(() => {
    if (filter === "all") {
      return portfolioProjects;
    }
    return portfolioProjects.filter((project) => project.category === filter);
  }, [filter]);

  const displayedProjects = useMemo(() => {
    return filteredProjects.slice(0, visibleProjects);
  }, [filteredProjects, visibleProjects]);

  const loadMore = () => {
    setVisibleProjects((prev) => prev + 4);
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("revealed");
          }
        });
      },
      { threshold: 0.1 }
    );

    // observe *all* scroll-reveal elements
    document.querySelectorAll(".scroll-reveal").forEach((el) => {
      // if you want the animation to replay when refiltering:
      el.classList.remove("revealed");
      observer.observe(el);
    });

    return () => {
      observer.disconnect();
    };
  }, [filter, visibleProjects]); // ← run again whenever filter or load-more changes

  return (
    <div className="min-h-screen bg-background pt-20">
      {/* Header Section */}
      <section className="relative py-16 md:py-20 lg:py-24 gradient-hero overflow-hidden">
        {/* Background Image - Portfolio/Creative Work Theme */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-50"
          style={{
            backgroundImage:
              "url(https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2815&q=80)",
          }}
        ></div>

        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/80 via-black/65 to-black/80"></div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            {/* Portfolio badge */}
            <div className="inline-flex items-center space-x-2 bg-primary/10 border border-primary/20 rounded-full px-6 py-3 mb-8 scroll-reveal">
              <Briefcase className="w-5 h-5 text-primary" />
              <span className="font-display font-medium text-primary">
                Our Work
              </span>
            </div>

            {/* Main heading */}
            <h1 className="font-display font-black text-4xl md:text-5xl lg:text-6xl mb-6 gradient-text-primary scroll-reveal">
              Portfolio & Success Stories
            </h1>

            {/* Description */}
            <p className="font-body text-lg md:text-xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed scroll-reveal">
              Explore our collection of transformative projects that showcase
              innovation, technical excellence, and measurable business impact
              across diverse industries.
            </p>

            {/* Stats grid */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-12 scroll-reveal">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-primary/20 to-purple-600/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Code className="w-8 h-8 text-primary" />
                </div>
                <div className="font-display font-black text-2xl md:text-3xl text-white mb-1">
                  50+
                </div>
                <div className="font-body text-gray-400 text-sm">Projects</div>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500/20 to-emerald-600/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Star className="w-8 h-8 text-green-400" />
                </div>
                <div className="font-display font-black text-2xl md:text-3xl text-white mb-1">
                  98%
                </div>
                <div className="font-body text-gray-400 text-sm">
                  Satisfaction
                </div>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500/20 to-cyan-600/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Globe className="w-8 h-8 text-blue-400" />
                </div>
                <div className="font-display font-black text-2xl md:text-3xl text-white mb-1">
                  25+
                </div>
                <div className="font-body text-gray-400 text-sm">
                  Industries
                </div>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-orange-500/20 to-red-600/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Users className="w-8 h-8 text-orange-400" />
                </div>
                <div className="font-display font-black text-2xl md:text-3xl text-white mb-1">
                  500K+
                </div>
                <div className="font-body text-gray-400 text-sm">
                  Users Impacted
                </div>
              </div>
            </div>

            {/* CTA Button */}
            <Button
              onClick={handleContactClick}
              className="btn-modern inline-flex items-center space-x-2 text-lg px-8 py-4 scroll-reveal"
            >
              <span>Start Your Project</span>
              <ArrowRight className="w-5 h-5" />
            </Button>
          </div>
        </div>
      </section>

      {/* Portfolio Projects Section */}
      <section className="py-16 gradient-section">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12 scroll-reveal">
            <h2 className="font-display font-bold text-3xl text-white mb-4">
              Our Portfolio
            </h2>
            <p className="font-body text-lg text-gray-400 max-w-2xl mx-auto">
              Filter by technology or industry to see relevant case studies
            </p>
          </div>

          {/* Category Filters */}
          <div className="mb-12 scroll-reveal">
            <div className="flex gap-3 overflow-x-auto pb-2 px-4 -mx-4 scrollbar-hide">
              {categories.map((category) => (
                <button
                  key={category.key}
                  onClick={() => setFilter(category.key)}
                  className={`px-6 py-3 rounded-xl font-display font-medium text-sm transition-all duration-300 whitespace-nowrap flex-shrink-0 ${
                    filter === category.key
                      ? "bg-primary text-white shadow-lg shadow-primary/25"
                      : "bg-white/5 text-gray-300 hover:bg-white/10 hover:text-white"
                  }`}
                >
                  {category.label}
                  <span className="ml-2 text-xs opacity-75">
                    ({category.count})
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* Portfolio Grid - 2 columns */}
          <div className="grid lg:grid-cols-2 gap-8 mb-12">
            {displayedProjects.map((project) => (
              <Card
                key={project.id}
                className="glass-strong border-white/10 hover-glow hover-lift scroll-reveal group overflow-hidden"
              >
                <div className="relative overflow-hidden">
                  <img
                    src={project.image}
                    alt={project.title}
                    className="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
                  <div className="absolute top-4 right-4">
                    <Badge className="bg-white/10 text-white border-white/20 text-xs">
                      {
                        categories.find((cat) => cat.key === project.category)
                          ?.label
                      }
                    </Badge>
                  </div>
                </div>

                <CardContent className="p-6">
                  <h3 className="font-display font-bold text-lg text-white mb-3 group-hover:text-primary transition-colors">
                    {project.title}
                  </h3>

                  <p className="font-body text-gray-300 text-sm leading-relaxed">
                    {project.description.length > 120
                      ? `${project.description.substring(0, 120)}...`
                      : project.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Load More Button */}
          {visibleProjects < filteredProjects.length && (
            <div className="text-center scroll-reveal">
              <Button
                onClick={loadMore}
                variant="outline"
                className="border-white/20 text-white hover:bg-primary/10 hover:border-primary/30 px-8 py-3"
              >
                Load More Projects
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          )}
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-gray-950/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 scroll-reveal">
            <h2 className="font-display font-bold text-4xl text-white mb-6">
              Our Impact in Numbers
            </h2>
            <p className="font-body text-xl text-gray-400 max-w-3xl mx-auto">
              Measurable results that speak for themselves
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 scroll-reveal">
            {[
              { number: "50+", label: "Projects Completed", icon: Code },
              { number: "98%", label: "Client Satisfaction", icon: Award },
              { number: "25+", label: "Industries Served", icon: Globe },
              { number: "500K+", label: "Users Impacted", icon: Users },
            ].map((stat, index) => (
              <div
                key={index}
                className="text-center glass-strong rounded-2xl p-8 hover-glow"
              >
                <div className="w-16 h-16 bg-gradient-to-br from-primary to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <stat.icon className="w-8 h-8 text-white" />
                </div>
                <div className="font-display font-black text-4xl text-primary mb-2">
                  {stat.number}
                </div>
                <div className="font-body text-gray-300">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary/10 to-purple-600/10 border-t border-white/10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-3xl mx-auto scroll-reveal">
            <h2 className="font-display font-bold text-4xl text-white mb-6">
              Ready to Create Your Success Story?
            </h2>
            <p className="font-body text-xl text-gray-300 mb-8 leading-relaxed">
              Join our growing portfolio of successful digital transformations.
              Let's discuss your project and how we can help you achieve
              remarkable results.
            </p>
            <Button
              onClick={handleContactClick}
              className="btn-modern text-lg px-8 py-4"
            >
              Start Your Project Today
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default PortfolioPage;
