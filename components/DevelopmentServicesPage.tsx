import React, { useEffect, useState } from "react";
import { But<PERSON> } from "./ui/button";
import {
  Code,
  Smartphone,
  Database,
  Globe,
  Layers,
  Shield,
  ArrowRight,
  CheckCircle,
  Settings,
  Zap,
  Target,
  TrendingUp,
  Users,
  Lightbulb,
  Rocket,
} from "lucide-react";

const DevelopmentServicesPage = () => {
  const [activeSection, setActiveSection] = useState<string | null>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("revealed");
          }
        });
      },
      { threshold: 0.1 }
    );

    const elements = document.querySelectorAll(".scroll-reveal");
    elements.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  const handleConnectClick = () => {
    const contactSection = document.getElementById("contact");
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <div className="min-h-screen bg-background pt-20">
      {/* Header Section */}
      <section className="relative py-12 sm:py-16 lg:py-20 gradient-hero overflow-hidden">
        {/* Background Image */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-55"
          style={{
            backgroundImage:
              "url(https://images.unsplash.com/photo-1461749280684-dccba630e2f6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2669&q=80)",
          }}
        ></div>

        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/75 via-black/55 to-black/75"></div>

        <div className="absolute inset-0 opacity-30">
          <div className="particles">
            {[...Array(50)].map((_, i) => (
              <div
                key={i}
                className="particle"
                style={{
                  left: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 15}s`,
                  animationDuration: `${15 + Math.random() * 10}s`,
                }}
              />
            ))}
          </div>
        </div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            <div className="inline-flex items-center space-x-2 bg-red-300 border border-white/10 rounded-full px-6 py-3 mb-8">
              <Code className="w-5 h-5 text-primary" />
              <span className="font-display font-medium text-primary">
                Development Solutions
              </span>
            </div>

            <h1 className="font-display font-black mb-6 gradient-text-primary">
              Development Services
            </h1>

            <p className="font-body text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              From web applications to mobile apps and APIs, we deliver
              cutting-edge development solutions that power your digital
              transformation and drive business growth.
            </p>

            <Button
              onClick={handleConnectClick}
              className="btn-modern inline-flex items-center space-x-2"
            >
              <span>Explore Our Development Services</span>
              <ArrowRight className="w-5 h-5" />
            </Button>
          </div>
        </div>
      </section>

      {/* Web Development Section */}
      <section id="web-development" className="py-24 gradient-section">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          {/* Section Header */}
          <div className="text-center max-w-4xl mx-auto mb-16 scroll-reveal">
            <div className="flex items-center justify-center space-x-3 mb-6">
              <div className="w-16 h-16 bg-gradient-to-br from-primary to-blue-600 rounded-2xl flex items-center justify-center">
                <Globe className="w-8 h-8 text-white" />
              </div>
              <div>
                <h2 className="font-display font-bold text-4xl text-white text-left">
                  Web Development
                </h2>
                <p className="font-body text-gray-400 text-left">
                  Full-stack web solutions
                </p>
              </div>
            </div>

            <h3 className="font-display font-semibold text-3xl text-white mb-6">
              A Web App Development Company with the Industry Expertise to
              Innovate
            </h3>

            <p className="font-body text-xl text-gray-300 mb-8 leading-relaxed">
              Black Lion is your premiere web app development company delivering
              software solutions that offer innovative digital experiences. Our
              agile culture and deep programming expertise ensure your project
              stays competitive.
            </p>

            <Button
              onClick={handleConnectClick}
              className="btn-modern text-lg px-8 py-4"
            >
              Let's Build Something Together
            </Button>
          </div>

          {/* Benefits Cards */}
          <div className="mb-16 scroll-reveal">
            <h4 className="font-display font-bold text-2xl text-white text-center mb-8">
              Why Choose Our Web Development Services
            </h4>
            <div className="grid md:grid-cols-3 lg:grid-cols-5 gap-6">
              {[
                {
                  icon: Users,
                  title: "Quality of talent",
                  desc: "Expert developers with proven track records",
                },
                {
                  icon: Target,
                  title: "End-to-end expertise",
                  desc: "Complete project lifecycle management",
                },
                {
                  icon: Lightbulb,
                  title: "Human-centered UI/UX",
                  desc: "User-focused design approach",
                },
                {
                  icon: TrendingUp,
                  title: "Scalable solutions",
                  desc: "Future-ready and growth-oriented",
                },
                {
                  icon: Rocket,
                  title: "Accelerated development",
                  desc: "Rapid delivery without compromising quality",
                },
              ].map((benefit, index) => (
                <div
                  key={index}
                  className="glass-strong rounded-2xl p-6 text-center hover-glow hover-lift"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-primary to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <benefit.icon className="w-6 h-6 text-white" />
                  </div>
                  <h5 className="font-display font-semibold text-white mb-2">
                    {benefit.title}
                  </h5>
                  <p className="font-body text-gray-400 text-sm">
                    {benefit.desc}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Services Grid */}
          <div className="scroll-reveal">
            <h4 className="font-display font-bold text-2xl text-white text-center mb-8">
              Our Web Development Services
            </h4>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  title: "Consultation & Planning",
                  desc: "Strategic roadmapping, technical assessment, and resource planning to ensure project success.",
                  deliverables: [
                    "Project roadmap",
                    "Technical assessment",
                    "Resource planning",
                  ],
                  icon: Target,
                },
                {
                  title: "UI/UX Design",
                  desc: "User-centered design approach with comprehensive wireframes and prototypes.",
                  deliverables: [
                    "Wireframes",
                    "Interactive prototypes",
                    "User journey mapping",
                  ],
                  icon: Lightbulb,
                },
                {
                  title: "Full-Stack Development",
                  desc: "Complete web application development from frontend to backend and APIs.",
                  deliverables: [
                    "Frontend development",
                    "Backend systems",
                    "API integration",
                  ],
                  icon: Code,
                },
                {
                  title: "QA & Security Testing",
                  desc: "Comprehensive testing and security audits to ensure robust applications.",
                  deliverables: [
                    "Performance testing",
                    "Security audits",
                    "Quality assurance",
                  ],
                  icon: Shield,
                },
                {
                  title: "Deployment & Launch",
                  desc: "Infrastructure setup, domain configuration, and team training for smooth launch.",
                  deliverables: [
                    "Infrastructure setup",
                    "Domain configuration",
                    "Team training",
                  ],
                  icon: Rocket,
                },
                {
                  title: "Maintenance & Support",
                  desc: "Ongoing support with bug fixes, monitoring, and regular updates.",
                  deliverables: [
                    "Bug fixes",
                    "System monitoring",
                    "Regular updates",
                  ],
                  icon: Settings,
                },
              ].map((service, index) => (
                <div
                  key={index}
                  className="glass-strong rounded-2xl p-8 hover-glow hover-lift"
                >
                  <div className="w-14 h-14 bg-gradient-to-br from-primary to-blue-600 rounded-xl flex items-center justify-center mb-6">
                    <service.icon className="w-7 h-7 text-white" />
                  </div>
                  <h5 className="font-display font-bold text-xl text-white mb-4">
                    {service.title}
                  </h5>
                  <p className="font-body text-gray-300 mb-6 leading-relaxed">
                    {service.desc}
                  </p>
                  <div className="space-y-2">
                    {service.deliverables.map((deliverable, idx) => (
                      <div key={idx} className="flex items-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-primary flex-shrink-0" />
                        <span className="font-body text-gray-400 text-sm">
                          {deliverable}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* CTA */}
          <div className="text-center mt-16 scroll-reveal">
            <p className="font-body text-xl text-gray-300 mb-6">
              Ready to transform your web presence?
            </p>
            <Button
              onClick={handleConnectClick}
              className="btn-modern text-lg px-8 py-4"
            >
              Let's Connect
            </Button>
          </div>
        </div>
      </section>

      {/* Mobile App Development Section */}
      <section id="mobile-development" className="py-24 bg-gray-950/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          {/* Section Header */}
          <div className="text-center max-w-4xl mx-auto mb-16 scroll-reveal">
            <div className="flex items-center justify-center space-x-3 mb-6">
              <div className="w-16 h-16 bg-gradient-to-br from-orange-600 to-red-600 rounded-2xl flex items-center justify-center">
                <Smartphone className="w-8 h-8 text-white" />
              </div>
              <div>
                <h2 className="font-display font-bold text-4xl text-white text-left">
                  Mobile App Development
                </h2>
                <p className="font-body text-gray-400 text-left">
                  Cross-platform mobile solutions
                </p>
              </div>
            </div>

            <h3 className="font-display font-semibold text-3xl text-white mb-6">
              Drive Engagement with Innovative Mobile Apps
            </h3>

            <p className="font-body text-xl text-gray-300 mb-8 leading-relaxed">
              As a globally recognized mobile development company, we deliver
              seamless brand experiences across platforms — from native apps to
              wearable tech and AI integrations.
            </p>

            <Button
              onClick={handleConnectClick}
              className="btn-modern text-lg px-8 py-4"
            >
              Let's Connect
            </Button>
          </div>

          {/* Key Features */}
          <div className="mb-16 scroll-reveal">
            <h4 className="font-display font-bold text-2xl text-white text-center mb-8">
              Key Features & Capabilities
            </h4>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[
                "Discovery & Roadmapping",
                "UX/UI Design",
                "Native & Cross-Platform Apps",
                "Wearables & Gaming Apps",
                "AI-Powered Mobile Apps",
                "App Store Launch & Maintenance",
              ].map((feature, index) => (
                <div
                  key={index}
                  className="glass-strong rounded-xl p-6 flex items-center space-x-4 hover-glow"
                >
                  <CheckCircle className="w-6 h-6 text-primary flex-shrink-0" />
                  <span className="font-body text-gray-200 font-medium">
                    {feature}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Services Grid */}
          <div className="scroll-reveal">
            <h4 className="font-display font-bold text-2xl text-white text-center mb-8">
              Our Mobile Development Services
            </h4>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  title: "Strategy & Planning",
                  desc: "Comprehensive mobile strategy with market insights and rapid prototyping.",
                  deliverables: [
                    "Mobile roadmap",
                    "Rapid prototyping",
                    "Market insights",
                  ],
                  icon: Target,
                },
                {
                  title: "UI/UX Design & CRO",
                  desc: "Data-driven design approach with conversion rate optimization.",
                  deliverables: [
                    "Data-driven design",
                    "Funnel optimization",
                    "User testing",
                  ],
                  icon: Lightbulb,
                },
                {
                  title: "App Development",
                  desc: "Native, cross-platform, wearable, gaming, and AI-powered mobile applications.",
                  deliverables: [
                    "Native development",
                    "Cross-platform apps",
                    "AI integration",
                  ],
                  icon: Smartphone,
                },
                {
                  title: "QA & Testing",
                  desc: "Comprehensive testing for security, compatibility, and usability.",
                  deliverables: [
                    "Security testing",
                    "Compatibility testing",
                    "Usability testing",
                  ],
                  icon: Shield,
                },
                {
                  title: "Launch & Support",
                  desc: "Go-to-market strategy, app store optimization, and ongoing updates.",
                  deliverables: [
                    "Go-to-market strategy",
                    "Store optimization",
                    "Regular updates",
                  ],
                  icon: Rocket,
                },
              ].map((service, index) => (
                <div
                  key={index}
                  className="glass-strong rounded-2xl p-8 hover-glow hover-lift"
                >
                  <div className="w-14 h-14 bg-gradient-to-br from-orange-600 to-red-600 rounded-xl flex items-center justify-center mb-6">
                    <service.icon className="w-7 h-7 text-white" />
                  </div>
                  <h5 className="font-display font-bold text-xl text-white mb-4">
                    {service.title}
                  </h5>
                  <p className="font-body text-gray-300 mb-6 leading-relaxed">
                    {service.desc}
                  </p>
                  <div className="space-y-2">
                    {service.deliverables.map((deliverable, idx) => (
                      <div key={idx} className="flex items-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-primary flex-shrink-0" />
                        <span className="font-body text-gray-400 text-sm">
                          {deliverable}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* CTA */}
          <div className="text-center mt-16 scroll-reveal">
            <p className="font-body text-xl text-gray-300 mb-6">
              Ready to get started with our mobile app development company?
            </p>
            <Button
              onClick={handleConnectClick}
              className="btn-modern text-lg px-8 py-4"
            >
              Let's Connect
            </Button>
          </div>
        </div>
      </section>

      {/* API Development Section */}
      <section id="api-development" className="py-24 gradient-section">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          {/* Section Header */}
          <div className="text-center max-w-4xl mx-auto mb-16 scroll-reveal">
            <div className="flex items-center justify-center space-x-3 mb-6">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center">
                <Layers className="w-8 h-8 text-white" />
              </div>
              <div>
                <h2 className="font-display font-bold text-4xl text-white text-left">
                  API Development
                </h2>
                <p className="font-body text-gray-400 text-left">
                  Scalable digital infrastructure
                </p>
              </div>
            </div>

            <h3 className="font-display font-semibold text-3xl text-white mb-6">
              Your Trusted API Partner for Scalable Digital Infrastructure
            </h3>

            <p className="font-body text-xl text-gray-300 mb-8 leading-relaxed">
              Black Lion delivers secure, scalable APIs that power modern
              applications. We handle full lifecycle management — strategy,
              design, development, testing, and optimization.
            </p>

            <Button
              onClick={handleConnectClick}
              className="btn-modern text-lg px-8 py-4"
            >
              Let's Build Something Together
            </Button>
          </div>

          {/* Benefits Cards */}
          <div className="mb-16 scroll-reveal">
            <h4 className="font-display font-bold text-2xl text-white text-center mb-8">
              API Development Benefits
            </h4>
            <div className="grid md:grid-cols-3 lg:grid-cols-5 gap-6">
              {[
                {
                  icon: Users,
                  title: "Top-tier talent",
                  desc: "Architectural experts with deep API knowledge",
                },
                {
                  icon: Target,
                  title: "Full lifecycle delivery",
                  desc: "End-to-end API development and management",
                },
                {
                  icon: Lightbulb,
                  title: "Developer-centric docs",
                  desc: "Comprehensive and intuitive documentation",
                },
                {
                  icon: TrendingUp,
                  title: "Cloud-native",
                  desc: "Future-proof and scalable solutions",
                },
                {
                  icon: Rocket,
                  title: "Accelerated DevOps",
                  desc: "Automated deployment and monitoring",
                },
              ].map((benefit, index) => (
                <div
                  key={index}
                  className="glass-strong rounded-2xl p-6 text-center hover-glow hover-lift"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-600 to-pink-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <benefit.icon className="w-6 h-6 text-white" />
                  </div>
                  <h5 className="font-display font-semibold text-white mb-2">
                    {benefit.title}
                  </h5>
                  <p className="font-body text-gray-400 text-sm">
                    {benefit.desc}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Services Grid */}
          <div className="scroll-reveal">
            <h4 className="font-display font-bold text-2xl text-white text-center mb-8">
              Our API Development Services
            </h4>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  title: "Consultation & Planning",
                  desc: "Strategic API planning with data flow analysis and governance frameworks.",
                  deliverables: [
                    "Data flow analysis",
                    "API strategy",
                    "Governance framework",
                  ],
                  icon: Target,
                },
                {
                  title: "API Design",
                  desc: "REST, GraphQL, and gRPC specifications with comprehensive mock servers.",
                  deliverables: [
                    "REST/GraphQL specs",
                    "gRPC design",
                    "Mock servers",
                  ],
                  icon: Lightbulb,
                },
                {
                  title: "API Development",
                  desc: "Secure API development with authentication, rate limiting, and integrations.",
                  deliverables: [
                    "Authentication systems",
                    "Rate limiting",
                    "Third-party integrations",
                  ],
                  icon: Layers,
                },
                {
                  title: "Testing & QA",
                  desc: "Automated testing suites with security and load testing capabilities.",
                  deliverables: [
                    "Automated test suites",
                    "Security testing",
                    "Load testing",
                  ],
                  icon: Shield,
                },
                {
                  title: "Deployment",
                  desc: "Containerized deployment with CI/CD pipelines and comprehensive monitoring.",
                  deliverables: [
                    "Docker/K8s deployment",
                    "CI/CD pipelines",
                    "Monitoring systems",
                  ],
                  icon: Rocket,
                },
                {
                  title: "Maintenance & Support",
                  desc: "Performance tuning, versioning management, and 24/7 support services.",
                  deliverables: [
                    "Performance tuning",
                    "Version management",
                    "24/7 support",
                  ],
                  icon: Settings,
                },
              ].map((service, index) => (
                <div
                  key={index}
                  className="glass-strong rounded-2xl p-8 hover-glow hover-lift"
                >
                  <div className="w-14 h-14 bg-gradient-to-br from-purple-600 to-pink-600 rounded-xl flex items-center justify-center mb-6">
                    <service.icon className="w-7 h-7 text-white" />
                  </div>
                  <h5 className="font-display font-bold text-xl text-white mb-4">
                    {service.title}
                  </h5>
                  <p className="font-body text-gray-300 mb-6 leading-relaxed">
                    {service.desc}
                  </p>
                  <div className="space-y-2">
                    {service.deliverables.map((deliverable, idx) => (
                      <div key={idx} className="flex items-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-primary flex-shrink-0" />
                        <span className="font-body text-gray-400 text-sm">
                          {deliverable}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* CTA */}
          <div className="text-center mt-16 scroll-reveal">
            <p className="font-body text-xl text-gray-300 mb-6">
              Ready to build scalable APIs that power your digital ecosystem?
            </p>
            <Button
              onClick={handleConnectClick}
              className="btn-modern text-lg px-8 py-4"
            >
              Let's Connect
            </Button>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary/10 to-purple-600/10 border-t border-white/10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-3xl mx-auto scroll-reveal">
            <h2 className="font-display font-bold text-4xl text-white mb-6">
              Ready to Build Your Next Digital Solution?
            </h2>
            <p className="font-body text-xl text-gray-300 mb-8 leading-relaxed">
              Let's discuss how our development expertise can transform your
              ideas into powerful digital experiences that drive results.
            </p>
            <Button
              onClick={handleConnectClick}
              className="btn-modern text-lg px-8 py-4"
            >
              Start Your Development Journey
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default DevelopmentServicesPage;
