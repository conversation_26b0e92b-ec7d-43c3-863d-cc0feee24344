import { Separator } from "./ui/separator";
import { Zap, Mail, Phone, Linkedin, Twitter } from "lucide-react";

const Footer = () => {
  const handleLogoClick = () => {
    window.location.hash = "";
  };

  return (
    <footer className="bg-black border-t border-gray-800">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Company Info */}
            <div className="md:col-span-2">
              <div
                className="flex items-center space-x-3 mb-4 cursor-pointer group w-fit"
                onClick={handleLogoClick}
              >
                <div className="w-10 h-10 bg-gradient-to-br from-primary to-purple-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <Zap className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="font-display font-bold text-white text-lg group-hover:text-primary transition-colors duration-300">
                    Black Lion Software
                  </h3>
                  <p className="font-body text-sm text-gray-400">
                    AI-Powered Digital Engineering
                  </p>
                </div>
              </div>
              <p className="text-gray-400 font-body leading-relaxed max-w-md">
                Transforming businesses through innovative AI-powered solutions
                and expert digital engineering.
              </p>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="font-display font-semibold text-white mb-4">
                Quick Links
              </h4>
              <div className="space-y-2">
                <a
                  href="#about-us"
                  className="block text-gray-400 hover:text-primary transition-colors font-body"
                >
                  About Us
                </a>
                <a
                  href="#ai-services"
                  className="block text-gray-400 hover:text-primary transition-colors font-body"
                >
                  Services
                </a>
                <a
                  href="#contact"
                  className="block text-gray-400 hover:text-primary transition-colors font-body"
                >
                  Contact
                </a>
              </div>
            </div>

            {/* Contact */}
            <div>
              <h4 className="font-display font-semibold text-white mb-4">
                Contact
              </h4>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Mail className="w-4 h-4 text-primary" />
                  <a
                    href="mailto:<EMAIL>"
                    className="text-gray-400 hover:text-white transition-colors font-body text-sm"
                  >
                    <EMAIL>
                  </a>
                </div>
                <div className="flex items-center space-x-3">
                  <Phone className="w-4 h-4 text-primary" />
                  <a
                    href="tel:+15551234567"
                    className="text-gray-400 hover:text-white transition-colors font-body text-sm"
                  >
                    +****************
                  </a>
                </div>
              </div>

              {/* Social Links */}
              <div className="flex space-x-3 mt-4">
                <a
                  href="https://linkedin.com/company/blacklionsoftware"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-8 h-8 bg-gray-800 hover:bg-primary rounded-lg flex items-center justify-center transition-colors duration-300"
                >
                  <Linkedin className="w-4 h-4 text-gray-400 hover:text-white" />
                </a>
                <a
                  href="https://twitter.com/blacklionsoftware"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-8 h-8 bg-gray-800 hover:bg-primary rounded-lg flex items-center justify-center transition-colors duration-300"
                >
                  <Twitter className="w-4 h-4 text-gray-400 hover:text-white" />
                </a>
              </div>
            </div>
          </div>
        </div>

        <Separator className="bg-gray-800" />

        {/* Bottom Bar */}
        <div className="py-6">
          <div className="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0">
            <div className="text-gray-400 font-body text-sm">
              © 2025 Black Lion Software. All rights reserved.
            </div>

            <div className="flex items-center space-x-6">
              <a
                href="#privacy"
                className="text-gray-400 hover:text-primary transition-colors font-body text-sm"
              >
                Privacy Policy
              </a>
              <a
                href="#terms"
                className="text-gray-400 hover:text-primary transition-colors font-body text-sm"
              >
                Terms of Service
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
