import { useEffect } from "react";
import { But<PERSON> } from "./ui/button";
import {
  <PERSON>,
  <PERSON><PERSON>,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  Target,
  TrendingUp,
  ArrowRight,
  CheckCircle,
} from "lucide-react";

const AIServicesPage = () => {
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("revealed");
          }
        });
      },
      { threshold: 0.1 }
    );

    const elements = document.querySelectorAll(".scroll-reveal");
    elements.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  const handleConnectClick = () => {
    const contactSection = document.getElementById("contact");
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <div className="min-h-screen bg-background pt-20">
      {/* Header Section */}
      <section className="relative py-12 sm:py-16 lg:py-20 gradient-hero overflow-hidden">
        {/* Background Image */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-55"
          style={{
            backgroundImage:
              "url(https://images.unsplash.com/photo-1677442136019-21780ecad995?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2532&q=80)",
          }}
        ></div>

        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/75 via-black/55 to-black/75"></div>

        <div className="absolute inset-0 opacity-30">
          <div className="particles">
            {[...Array(50)].map((_, i) => (
              <div
                key={i}
                className="particle"
                style={{
                  left: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 15}s`,
                  animationDuration: `${15 + Math.random() * 10}s`,
                }}
              />
            ))}
          </div>
        </div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            <div className="inline-flex items-center space-x-2 bg-primary/10 border border-white/10 rounded-full px-6 py-3 mb-6 sm:mb-8">
              <Brain className="w-5 h-5 text-primary" />
              <span className="font-display font-medium text-primary">
                Artificial Intelligence Solutions
              </span>
            </div>

            <h1 className="font-display font-black mb-4 sm:mb-6 gradient-text-primary text-[32px]">
              Artificial Intelligence
            </h1>

            <p className="font-body text-xl text-gray-300 mb-6 sm:mb-8 max-w-3xl mx-auto leading-relaxed text-[15px] text-[16px]">
              Integrate GenAI for unprecedented efficiency and growth. Black
              Lion is a trusted AI software development company that helps
              organizations of all sizes get more value from their data.
            </p>

            <Button
              onClick={handleConnectClick}
              className="btn-modern inline-flex items-center space-x-2"
            >
              <span>Explore Our AI Solutions</span>
              <ArrowRight className="w-5 h-5" />
            </Button>
          </div>
        </div>
      </section>

      {/* Generative AI Section */}
      <section id="generative-ai" className="py-20 gradient-section">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="scroll-reveal">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-primary to-purple-600 rounded-xl flex items-center justify-center">
                  <Sparkles className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="font-display font-bold text-white">
                    Generative AI
                  </h2>
                  <p className="font-body text-gray-400 text-sm">
                    Next-generation AI solutions
                  </p>
                </div>
              </div>

              <p className="font-body text-gray-300 text-lg mb-8 leading-relaxed">
                Integrate GenAI for unprecedented efficiency and growth. Black
                Lion is a trusted AI software development company that helps
                organizations of all sizes get more value from their data.
              </p>

              <h3 className="font-display font-semibold text-2xl text-white mb-6">
                Elevate your Business with Generative AI
              </h3>

              <p className="font-body text-gray-300 mb-8 leading-relaxed">
                Seamlessly integrate generative AI into your workflows to drive
                innovation and efficiency. Services include:
              </p>

              <div className="grid sm:grid-cols-2 gap-4 mb-8">
                {[
                  "Generative Models (chatbots, code generation, synthetic data, forecasting)",
                  "Natural Language Processing (language understanding, generation, sentiment analysis, translation)",
                  "Speech and Audio Models (speech-to-text, text-to-speech, emotion detection, enhancement)",
                  "Recommendation Systems (personalized and group suggestions, AI for recruitment)",
                ].map((service, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <CheckCircle className="w-5 h-5 text-primary mt-1 flex-shrink-0" />
                    <span className="font-body text-gray-300 text-sm leading-relaxed">
                      {service}
                    </span>
                  </div>
                ))}
              </div>

              <Button onClick={handleConnectClick} className="btn-modern">
                Let's Connect
              </Button>
            </div>

            <div className="scroll-reveal lg:order-first">
              <div className="relative">
                <div className="glass-strong rounded-3xl p-8 hover-glow">
                  <div className="grid grid-cols-2 gap-6">
                    <div className="text-center">
                      <div
                        className="w-16 h-16 bg-gradient-to-br from-primary to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4 animate-float"
                        style={{ animationDelay: "0s" }}
                      >
                        <Brain className="w-8 h-8 text-white" />
                      </div>
                      <h4 className="font-display font-semibold text-white mb-2">
                        Smart Models
                      </h4>
                      <p className="font-body text-gray-400 text-sm">
                        Advanced AI algorithms
                      </p>
                    </div>
                    <div className="text-center">
                      <div
                        className="w-16 h-16 bg-gradient-to-br from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-4 animate-float"
                        style={{ animationDelay: "0.5s" }}
                      >
                        <Sparkles className="w-8 h-8 text-white" />
                      </div>
                      <h4 className="font-display font-semibold text-white mb-2">
                        Generation
                      </h4>
                      <p className="font-body text-gray-400 text-sm">
                        Content & code creation
                      </p>
                    </div>
                    <div className="text-center">
                      <div
                        className="w-16 h-16 bg-gradient-to-br from-pink-600 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-4 animate-float"
                        style={{ animationDelay: "1s" }}
                      >
                        <Target className="w-8 h-8 text-white" />
                      </div>
                      <h4 className="font-display font-semibold text-white mb-2">
                        Precision
                      </h4>
                      <p className="font-body text-gray-400 text-sm">
                        Accurate predictions
                      </p>
                    </div>
                    <div className="text-center">
                      <div
                        className="w-16 h-16 bg-gradient-to-br from-red-600 to-orange-600 rounded-2xl flex items-center justify-center mx-auto mb-4 animate-float"
                        style={{ animationDelay: "1.5s" }}
                      >
                        <TrendingUp className="w-8 h-8 text-white" />
                      </div>
                      <h4 className="font-display font-semibold text-white mb-2">
                        Growth
                      </h4>
                      <p className="font-body text-gray-400 text-sm">
                        Scalable solutions
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Machine Learning Section */}
      <section id="machine-learning" className="py-20 bg-gray-950/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="scroll-reveal">
              <div className="relative">
                <div className="glass-strong rounded-3xl p-8 hover-glow">
                  <div className="text-center mb-8">
                    <div className="w-20 h-20 bg-gradient-to-br from-primary to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 animate-pulse-glow">
                      <Cpu className="w-10 h-10 text-white" />
                    </div>
                    <h4 className="font-display font-bold text-2xl text-white mb-4">
                      Machine Learning Pipeline
                    </h4>
                  </div>

                  <div className="space-y-4">
                    {[
                      {
                        step: "01",
                        title: "Data Collection",
                        desc: "Gather and prepare training data",
                      },
                      {
                        step: "02",
                        title: "Model Training",
                        desc: "Build and optimize ML models",
                      },
                      {
                        step: "03",
                        title: "Deployment",
                        desc: "Deploy models to production",
                      },
                      {
                        step: "04",
                        title: "Monitoring",
                        desc: "Continuous performance tracking",
                      },
                    ].map((item, index) => (
                      <div
                        key={index}
                        className="flex items-center space-x-4 p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-colors"
                      >
                        <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center flex-shrink-0">
                          <span className="font-display font-bold text-white text-sm">
                            {item.step}
                          </span>
                        </div>
                        <div>
                          <h5 className="font-display font-semibold text-white">
                            {item.title}
                          </h5>
                          <p className="font-body text-gray-400 text-sm">
                            {item.desc}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            <div className="scroll-reveal">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-cyan-600 rounded-xl flex items-center justify-center">
                  <Cpu className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="font-display font-bold text-white">
                    Machine Learning
                  </h2>
                  <p className="font-body text-gray-400 text-sm">
                    Intelligent automation solutions
                  </p>
                </div>
              </div>

              <p className="font-body text-gray-300 text-lg mb-8 leading-relaxed">
                Automate complex data analysis to improve decision-making.
              </p>

              <h3 className="font-display font-semibold text-2xl text-white mb-6">
                Personalize Experiences &amp; Predictive Insights with ML
              </h3>

              <p className="font-body text-gray-300 mb-8 leading-relaxed">
                We provide end-to-end machine learning services including:
              </p>

              <div className="space-y-4 mb-8">
                {[
                  "ML Consulting (strategy, optimization, infrastructure)",
                  "Model Development (data prep, training, deployment, monitoring)",
                  "MLOps (CI/CD, governance, retraining, compliance)",
                ].map((service, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <CheckCircle className="w-5 h-5 text-primary mt-1 flex-shrink-0" />
                    <span className="font-body text-gray-300 leading-relaxed">
                      {service}
                    </span>
                  </div>
                ))}
              </div>

              <p className="font-body text-gray-300 mb-6">
                Ready to get started?
              </p>

              <Button onClick={handleConnectClick} className="btn-modern">
                Let's Connect
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Data Analytics Section */}
      <section id="data-analytics" className="py-20 gradient-section">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="scroll-reveal">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-green-600 to-emerald-600 rounded-xl flex items-center justify-center">
                  <BarChart3 className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="font-display font-bold text-white">
                    Data Analytics
                  </h2>
                  <p className="font-body text-gray-400 text-sm">
                    Transform data into insights
                  </p>
                </div>
              </div>

              <p className="font-body text-gray-300 text-lg mb-8 leading-relaxed">
                Unlock actionable insights and streamline decision-making with
                executive dashboards.
              </p>

              <h3 className="font-display font-semibold text-2xl text-white mb-6">
                Elevate Your Business with Advanced Analytics
              </h3>

              <p className="font-body text-gray-300 mb-8 leading-relaxed">
                We offer full-spectrum analytics services:
              </p>

              <div className="space-y-4 mb-8">
                {[
                  "Analytics Consulting (BI, data strategy, compliance)",
                  "AI & Data Science (predictive analytics, NLP, recommendation engines, image/video analysis)",
                  "Data Engineering (migration, transformation, modernization, augmentation)",
                  "Analytics Platform Implementation (data lakes, data warehouses, reporting, dashboards)",
                ].map((service, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <CheckCircle className="w-5 h-5 text-primary mt-1 flex-shrink-0" />
                    <span className="font-body text-gray-300 leading-relaxed">
                      {service}
                    </span>
                  </div>
                ))}
              </div>

              <p className="font-body text-gray-300 mb-6">
                Ready to get started with our data analytics consulting
                services?
              </p>

              <Button onClick={handleConnectClick} className="btn-modern">
                Let's Connect
              </Button>
            </div>

            <div className="scroll-reveal lg:order-first">
              <div className="relative">
                <div className="glass-strong rounded-3xl p-8 hover-glow">
                  <div className="text-center mb-8">
                    <div className="w-20 h-20 bg-gradient-to-br from-green-600 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-6 animate-pulse-glow">
                      <BarChart3 className="w-10 h-10 text-white" />
                    </div>
                    <h4 className="font-display font-bold text-2xl text-white mb-4">
                      Analytics Dashboard
                    </h4>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    {[
                      { metric: "10x", label: "Faster Insights" },
                      { metric: "95%", label: "Accuracy Rate" },
                      { metric: "24/7", label: "Real-time Monitoring" },
                      { metric: "∞", label: "Scalable Solutions" },
                    ].map((item, index) => (
                      <div
                        key={index}
                        className="text-center p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-colors"
                      >
                        <div className="font-display font-black text-3xl text-primary mb-2">
                          {item.metric}
                        </div>
                        <div className="font-body text-gray-300 text-sm">
                          {item.label}
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="mt-8 p-4 bg-gradient-to-r from-primary/20 to-green-600/20 rounded-xl border border-primary/30">
                    <p className="font-body text-white text-center text-sm">
                      Transform your raw data into actionable business
                      intelligence
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary/10 to-purple-600/10 border-t border-white/10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-3xl mx-auto scroll-reveal">
            <h2 className="font-display font-bold text-4xl text-white mb-6">
              Ready to Transform Your Business with AI?
            </h2>
            <p className="font-body text-xl text-gray-300 mb-8 leading-relaxed">
              Let's discuss how our AI solutions can drive unprecedented growth
              and efficiency for your organization.
            </p>
            <Button
              onClick={handleConnectClick}
              className="btn-modern text-lg px-8 py-4"
            >
              Start Your AI Journey Today
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default AIServicesPage;
