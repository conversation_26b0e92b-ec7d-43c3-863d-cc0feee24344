import React, { useEffect, useState } from 'react';
import { But<PERSON> } from './ui/button';
import { Brain, Target, Lightbulb, Users, TrendingUp, Settings, ArrowRight, CheckCircle, Zap, Rocket, Award, Globe, Shield, Star } from 'lucide-react';

const DigitalStrategyPage = () => {
  const [activeSection, setActiveSection] = useState<string | null>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('revealed');
          }
        });
      },
      { threshold: 0.1 }
    );

    const elements = document.querySelectorAll('.scroll-reveal');
    elements.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  const handleConnectClick = () => {
    const contactSection = document.getElementById('contact');
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="min-h-screen bg-background pt-20">
      {/* Header Section */}
      <section className="relative py-12 sm:py-16 lg:py-20 gradient-hero overflow-hidden">
        {/* Background Image */}
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-55"
          style={{
            backgroundImage: 'url(https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2815&q=80)'
          }}
        ></div>
        
        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/75 via-black/55 to-black/75"></div>
        
        <div className="absolute inset-0 opacity-30">
          <div className="particles">
            {[...Array(50)].map((_, i) => (
              <div
                key={i}
                className="particle"
                style={{
                  left: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 15}s`,
                  animationDuration: `${15 + Math.random() * 10}s`
                }}
              />
            ))}
          </div>
        </div>
        
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            <div className="inline-flex items-center space-x-2 bg-primary/10 border border-primary/20 rounded-full px-6 py-3 mb-8">
              <Brain className="w-5 h-5 text-primary" />
              <span className="font-display font-medium text-primary">Strategic Consulting</span>
            </div>
            
            <h1 className="font-display font-black mb-6 gradient-text-primary">
              Digital Strategy Consulting
            </h1>
            
            <p className="font-body text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              Collaborate with Black Lion to accelerate digital transformation and seamlessly integrate AI.
            </p>
            
            <p className="font-body text-lg text-gray-400 mb-8 max-w-3xl mx-auto leading-relaxed">
              Black Lion empowers businesses to innovate and maximize efficiency by leveraging digital strategy, AI/ML, and emerging technologies. We guide organizations through complex transformations to thrive in a fast-changing landscape.
            </p>
            
            <Button 
              onClick={handleConnectClick}
              className="btn-modern inline-flex items-center space-x-2 text-lg px-8 py-4"
            >
              <span>Let's Connect</span>
              <ArrowRight className="w-5 h-5" />
            </Button>
          </div>
        </div>
      </section>

      {/* Guiding Businesses Section */}
      <section className="py-24 gradient-section">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto mb-16 scroll-reveal">
            <div className="flex items-center justify-center space-x-3 mb-6">
              <div className="w-16 h-16 bg-gradient-to-br from-primary to-purple-600 rounded-2xl flex items-center justify-center">
                <Rocket className="w-8 h-8 text-white" />
              </div>
              <div>
                <h2 className="font-display font-bold text-4xl text-white text-left">Guiding Businesses Through</h2>
                <p className="font-body text-gray-400 text-left">Transformative Journeys</p>
              </div>
            </div>
            
            <h3 className="font-display font-semibold text-3xl text-white mb-6">
              Guiding Businesses Through Transformative Journeys
            </h3>
            
            <p className="font-body text-xl text-gray-300 leading-relaxed">
              Black Lion transforms outdated systems into modern, high-value solutions. From AI-powered operations to cloud optimization and customer journey mapping, we help your business make strategic technology decisions with confidence.
            </p>
          </div>
        </div>
      </section>

      {/* Services We Offer Section */}
      <section className="py-24 bg-gray-950/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 scroll-reveal">
            <h2 className="font-display font-bold text-4xl text-white mb-6">Services We Offer</h2>
            <p className="font-body text-xl text-gray-400 max-w-3xl mx-auto">
              Comprehensive digital strategy solutions tailored to your business needs
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 scroll-reveal">
            {[
              {
                title: 'Business Process Optimization Consulting',
                desc: 'Identify inefficiencies and integrate digital tools to enhance workflows, boost productivity, and deliver more value to customers.',
                icon: Settings,
                features: ['Workflow analysis', 'Digital tool integration', 'Productivity enhancement', 'Customer value optimization']
              },
              {
                title: 'Technology Strategy & Implementation',
                desc: 'Evaluate current platforms and recommend high-impact solutions (AI/ML, cloud, analytics) tailored for performance, scalability, and cost-effectiveness.',
                icon: Target,
                features: ['Platform evaluation', 'AI/ML recommendations', 'Cloud solutions', 'Cost optimization']
              },
              {
                title: 'Product Strategy',
                desc: 'From ideation to MVP in less than 2 weeks, we help define, prototype, and scale innovative products with our LPA framework.',
                icon: Lightbulb,
                features: ['Rapid ideation', 'MVP development', 'LPA framework', 'Product scaling']
              },
              {
                title: 'Learning & Development',
                desc: 'Build a resilient, future-proof organization with tailored learning solutions that align with your mission and drive results.',
                icon: Users,
                features: ['Custom training programs', 'Skill development', 'Future-proofing', 'Mission alignment']
              }
            ].map((service, index) => (
              <div key={index} className="glass-strong rounded-2xl p-8 hover-glow hover-lift">
                <div className="flex items-center space-x-4 mb-6">
                  <div className="w-14 h-14 bg-gradient-to-br from-primary to-purple-600 rounded-xl flex items-center justify-center">
                    <service.icon className="w-7 h-7 text-white" />
                  </div>
                  <h3 className="font-display font-bold text-xl text-white flex-1">{service.title}</h3>
                </div>
                <p className="font-body text-gray-300 mb-6 leading-relaxed">{service.desc}</p>
                <div className="grid grid-cols-2 gap-3">
                  {service.features.map((feature, idx) => (
                    <div key={idx} className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-primary flex-shrink-0" />
                      <span className="font-body text-gray-400 text-sm">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Advantages Section */}
      <section className="py-24 gradient-section">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 scroll-reveal">
            <h2 className="font-display font-bold text-4xl text-white mb-6">Advantages of Working with Black Lion</h2>
            <p className="font-body text-xl text-gray-400 max-w-3xl mx-auto">
              Why industry leaders choose us as their strategic digital transformation partner
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 scroll-reveal">
            {[
              {
                title: 'Domain Expertise',
                desc: 'Deep knowledge across industries enables highly tailored digital strategy solutions.',
                icon: Award
              },
              {
                title: 'Business Alignment',
                desc: 'We align all stakeholders on strategic goals to ensure smooth execution and adoption.',
                icon: Target
              },
              {
                title: 'Process Automation',
                desc: 'Uncover inefficiencies and implement automation that increases ROI and innovation.',
                icon: Settings
              },
              {
                title: 'End-to-End Digital Partner',
                desc: 'We support every project phase—from design and development to testing and ongoing support.',
                icon: Globe
              },
              {
                title: 'Elite Tech Talent',
                desc: 'Access global experts in 100+ technologies, including emerging AI/ML specializations.',
                icon: Users
              },
              {
                title: 'Proven Track Record',
                desc: 'Trusted by leading enterprises and startups for mission-critical strategy consulting.',
                icon: Star
              }
            ].map((advantage, index) => (
              <div key={index} className="glass-strong rounded-2xl p-8 text-center hover-glow hover-lift">
                <div className="w-16 h-16 bg-gradient-to-br from-primary to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <advantage.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-display font-bold text-xl text-white mb-4">{advantage.title}</h3>
                <p className="font-body text-gray-300 leading-relaxed">{advantage.desc}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary/10 to-purple-600/10 border-t border-white/10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-3xl mx-auto scroll-reveal">
            <h2 className="font-display font-bold text-4xl text-white mb-6">
              Ready to get started?
            </h2>
            <p className="font-body text-xl text-gray-300 mb-8 leading-relaxed">
              Let's discuss how our digital strategy expertise can transform your business and accelerate your digital transformation journey.
            </p>
            <Button onClick={handleConnectClick} className="btn-modern text-lg px-8 py-4">
              Let's Connect
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default DigitalStrategyPage;