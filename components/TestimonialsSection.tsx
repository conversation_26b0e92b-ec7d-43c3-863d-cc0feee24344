import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Star, Quote, CheckCircle } from 'lucide-react';

const TestimonialsSection = () => {
  const [currentPair, setCurrentPair] = useState(0);

  const testimonials = [
    {
      quote: "Black Lion Software transformed our legacy system into a modern, AI-powered platform that increased our operational efficiency by 300%. Their expertise in both technology and business strategy is unmatched.",
      name: "<PERSON>",
      title: "Chief Technology Officer",
      company: "TechFlow Solutions",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face",
      result: "+300% Efficiency",
      industry: "FinTech"
    },
    {
      quote: "The team's ability to understand our complex requirements and deliver a scalable solution within budget and timeline was exceptional. They truly became an extension of our team.",
      name: "<PERSON>",
      title: "Head of Digital Innovation", 
      company: "Global Finance Corp",
      image: "https://images.unsplash.com/photo-*************-5658abf4ff4e?w=400&h=400&fit=crop&crop=face",
      result: "On-Time Delivery",
      industry: "Banking"
    },
    {
      quote: "Working with Black Lion Software was a game-changer for our startup. Their product mindset and technical excellence helped us launch our MVP in record time and secure Series A funding.",
      name: "Emily Johnson",
      title: "Founder & CEO",
      company: "HealthTech Innovations",
      image: "https://images.unsplash.com/photo-*************-15a19d654956?w=400&h=400&fit=crop&crop=face",
      result: "Series A Secured",
      industry: "Healthcare"
    },
    {
      quote: "The AI solution they developed for our e-commerce platform resulted in a 45% increase in conversion rates. Their deep understanding of machine learning and user behavior is remarkable.",
      name: "David Park",
      title: "VP of Technology",
      company: "RetailMax",
      image: "https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face",
      result: "+45% Conversions",
      industry: "E-commerce"
    },
    {
      quote: "From concept to deployment, Black Lion Software delivered a world-class mobile application that our users love. Their attention to detail and commitment to quality is outstanding.",
      name: "Lisa Thompson",
      title: "Product Manager",
      company: "MobileFirst Inc",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face",
      result: "5★ App Store",
      industry: "Mobile Apps"
    },
    {
      quote: "Their blockchain implementation revolutionized our supply chain transparency. The solution they built handles millions of transactions daily with zero downtime.",
      name: "James Wilson",
      title: "Supply Chain Director",
      company: "LogiTech Global",
      image: "https://images.unsplash.com/photo-1560250097-0b93528c311a?w=400&h=400&fit=crop&crop=face",
      result: "Zero Downtime",
      industry: "Logistics"
    }
  ];

  // Calculate total pairs
  const totalPairs = Math.ceil(testimonials.length / 2);

  const nextPair = () => {
    setCurrentPair((prev) => 
      prev === totalPairs - 1 ? 0 : prev + 1
    );
  };

  const prevPair = () => {
    setCurrentPair((prev) => 
      prev === 0 ? totalPairs - 1 : prev - 1
    );
  };

  const goToPair = (index: number) => {
    setCurrentPair(index);
  };

  // Get current testimonials to display
  const getCurrentTestimonials = () => {
    const startIndex = currentPair * 2;
    return testimonials.slice(startIndex, startIndex + 2);
  };

  const currentTestimonials = getCurrentTestimonials();

  const TestimonialCard = ({ testimonial }: { testimonial: typeof testimonials[0] }) => (
    <div className="bg-white/[0.02] backdrop-blur-sm border border-white/10 rounded-2xl p-8 relative h-full flex flex-col">
      {/* Quote Icon */}
      <div className="absolute top-6 right-6 w-8 h-8 flex items-center justify-center text-primary/30">
        <Quote className="w-6 h-6" />
      </div>

      {/* Rating */}
      <div className="flex items-center gap-2 mb-6">
        <div className="flex items-center">
          {[...Array(5)].map((_, i) => (
            <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
          ))}
        </div>
        <span className="text-gray-400 font-body text-sm">5.0</span>
      </div>

      {/* Quote */}
      <blockquote className="text-lg text-white font-body leading-relaxed mb-8 flex-grow text-pretty">
        "{testimonial.quote}"
      </blockquote>

      {/* Author Section */}
      <div className="space-y-4">
        {/* Author Info */}
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-primary/30 flex-shrink-0">
            <img
              src={testimonial.image}
              alt={testimonial.name}
              className="w-full h-full object-cover"
            />
          </div>
          
          <div className="min-w-0 flex-1">
            <h4 className="font-display font-bold text-white text-base mb-1">
              {testimonial.name}
            </h4>
            <p className="text-gray-300 font-body text-sm mb-1">
              {testimonial.title}
            </p>
            <p className="text-gray-500 font-body text-sm">
              {testimonial.company}
            </p>
          </div>
        </div>

        {/* Result & Industry */}
        <div className="flex items-center justify-between pt-4 border-t border-white/10">
          <div className="flex items-center gap-2 bg-primary/10 border border-primary/20 rounded-full px-3 py-1">
            <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
            <span className="text-primary font-display font-medium text-xs">
              {testimonial.result}
            </span>
          </div>
          
          <span className="text-gray-400 font-body text-xs">
            {testimonial.industry}
          </span>
        </div>
      </div>
    </div>
  );

  return (
    <section className="py-20 lg:py-32 relative overflow-hidden">
      {/* Minimal Background */}
      <div className="absolute inset-0 bg-gradient-to-b from-background via-gray-950/50 to-background">
        {/* Subtle accent */}
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[800px] h-[400px] bg-primary/3 rounded-full blur-3xl"></div>
      </div>

      <div className="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8">
        {/* Clean Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-2 text-primary text-sm font-display font-medium mb-4 uppercase tracking-wider">
            <CheckCircle className="w-4 h-4" />
            Client Success Stories
          </div>
          
          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-display font-black text-white mb-6">
            What Our <span className="gradient-text-primary">Clients Say</span>
          </h2>
          
          <p className="text-xl text-gray-400 max-w-2xl mx-auto font-body leading-relaxed">
            Real results from real partnerships that transformed businesses.
          </p>
        </div>

        {/* Testimonials Grid */}
        <div className="max-w-7xl mx-auto">
          <div className="relative mb-16">
            {/* Testimonials Container */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 min-h-[400px]">
              {currentTestimonials.map((testimonial, index) => (
                <div
                  key={`${currentPair}-${index}`}
                  className="animate-fade-in-up"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <TestimonialCard testimonial={testimonial} />
                </div>
              ))}
            </div>
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-between">
            {/* Previous */}
            <button
              onClick={prevPair}
              className="group flex items-center gap-3 text-gray-400 hover:text-white transition-colors duration-300"
            >
              <div className="w-12 h-12 border border-gray-700 rounded-full flex items-center justify-center group-hover:border-primary/50 transition-colors duration-300">
                <ChevronLeft className="w-5 h-5" />
              </div>
              <span className="hidden sm:block font-display font-medium">Previous</span>
            </button>

            {/* Dots */}
            <div className="flex items-center gap-2">
              {Array.from({ length: totalPairs }).map((_, index) => (
                <button
                  key={index}
                  onClick={() => goToPair(index)}
                  className={`transition-all duration-300 ${
                    index === currentPair
                      ? 'w-8 h-2 bg-primary rounded-full'
                      : 'w-2 h-2 bg-gray-600 rounded-full hover:bg-gray-500'
                  }`}
                />
              ))}
            </div>

            {/* Next */}
            <button
              onClick={nextPair}
              className="group flex items-center gap-3 text-gray-400 hover:text-white transition-colors duration-300"
            >
              <span className="hidden sm:block font-display font-medium">Next</span>
              <div className="w-12 h-12 border border-gray-700 rounded-full flex items-center justify-center group-hover:border-primary/50 transition-colors duration-300">
                <ChevronRight className="w-5 h-5" />
              </div>
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;