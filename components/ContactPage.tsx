import React, { useState } from "react";
import { But<PERSON> } from "./ui/button";
import { Input } from "./ui/input";
import { Textarea } from "./ui/textarea";
import { Card, CardContent } from "./ui/card";
import { Badge } from "./ui/badge";
import {
  Mail,
  Phone,
  Clock,
  Send,
  CheckCircle,
  Globe,
  Linkedin,
  Twitter,
  Calendar,
  ArrowRight,
  Users,
  Zap,
} from "lucide-react";

const ContactPage = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    company: "",
    phone: "",
    project: "",
    budget: "",
    timeline: "",
    message: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    await new Promise((resolve) => setTimeout(resolve, 2000));

    setIsSubmitting(false);
    setIsSubmitted(true);

    // Reset form after 3 seconds
    setTimeout(() => {
      setIsSubmitted(false);
      setFormData({
        name: "",
        email: "",
        company: "",
        phone: "",
        project: "",
        budget: "",
        timeline: "",
        message: "",
      });
    }, 3000);
  };

  const contactMethods = [
    {
      icon: Mail,
      title: "Email Us",
      description: "Get in touch via email",
      value: "<EMAIL>",
      action: "mailto:<EMAIL>",
    },
    {
      icon: Phone,
      title: "Call Us",
      description: "Speak with our team",
      value: "+****************",
      action: "tel:+15551234567",
    },
    {
      icon: Calendar,
      title: "Schedule a Call",
      description: "Book a consultation",
      value: "30-min free consultation",
      action: "#",
    },
  ];

  const projectTypes = [
    "Web Development",
    "Mobile App Development",
    "AI/ML Solutions",
    "Cloud Infrastructure",
    "Digital Strategy",
    "Other",
  ];

  const budgetRanges = [
    "$5,000 - $15,000",
    "$15,000 - $50,000",
    "$50,000 - $100,000",
    "$100,000+",
    "Let's Discuss",
  ];

  const timelines = [
    "1-2 months",
    "3-6 months",
    "6-12 months",
    "12+ months",
    "Flexible",
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative pt-24 sm:pt-28 lg:pt-32 pb-12 sm:pb-16 lg:pb-20">
        {/* Background Image */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-50"
          style={{
            backgroundImage:
              "url(https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2670&q=80)",
          }}
        ></div>

        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/75 via-black/55 to-black/75"></div>

        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-purple-500/5"></div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <Badge className="mb-8 bg-primary/10 text-primary border-primary/20 px-4 py-2">
              <Zap className="w-4 h-4 mr-2" />
              Let's Build Together
            </Badge>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-display font-black text-white mb-6 leading-tight">
              Ready to Start Your Project?
            </h1>
            <p className="text-xl text-gray-300 font-body max-w-2xl mx-auto leading-relaxed">
              Transform your ideas into reality. Tell us about your project and
              we'll get back to you within 24 hours.
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="pb-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            {/* Contact Methods */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16">
              {contactMethods.map((method, index) => (
                <Card
                  key={index}
                  className="glass border-white/10 hover:border-primary/30 transition-all duration-300 group cursor-pointer"
                >
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 bg-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-primary/20 transition-colors duration-300">
                      <method.icon className="w-8 h-8 text-primary" />
                    </div>
                    <h3 className="text-xl font-display font-bold text-white mb-2">
                      {method.title}
                    </h3>
                    <p className="text-gray-400 font-body mb-4">
                      {method.description}
                    </p>
                    <p className="text-primary font-display font-semibold mb-6">
                      {method.value}
                    </p>
                    <Button
                      variant="outline"
                      className="border-white/20 text-white hover:bg-primary/10 hover:border-primary/30 group-hover:scale-105 transition-all duration-300"
                      onClick={() => {
                        if (
                          method.action.startsWith("mailto:") ||
                          method.action.startsWith("tel:")
                        ) {
                          window.location.href = method.action;
                        }
                      }}
                    >
                      Get Started
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Contact Form */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
              {/* Contact Info Sidebar */}
              <div className="lg:col-span-1">
                <div className="sticky top-32">
                  <h2 className="text-2xl font-display font-bold text-white mb-6">
                    Get In Touch
                  </h2>
                  <p className="text-gray-300 font-body mb-8 leading-relaxed">
                    Ready to discuss your project? We're here to help you bring
                    your vision to life with cutting-edge technology and expert
                    guidance.
                  </p>

                  <div className="space-y-6 mb-8">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                        <Clock className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <p className="text-white font-display font-medium">
                          Response Time
                        </p>
                        <p className="text-gray-400 font-body">
                          Within 24 hours
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                        <Users className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <p className="text-white font-display font-medium">
                          Expert Team
                        </p>
                        <p className="text-gray-400 font-body">
                          Dedicated project manager
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                        <Globe className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <p className="text-white font-display font-medium">
                          Global Reach
                        </p>
                        <p className="text-gray-400 font-body">
                          25+ countries served
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <p className="text-sm text-gray-400 font-body">
                      Connect with us
                    </p>
                    <div className="flex space-x-4">
                      <a
                        href="https://linkedin.com/company/blacklionsoftware"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-12 h-12 bg-white/5 hover:bg-blue-600/10 border border-white/10 hover:border-blue-400/30 rounded-xl flex items-center justify-center transition-all duration-300"
                      >
                        <Linkedin className="w-6 h-6 text-gray-400 hover:text-blue-400" />
                      </a>
                      <a
                        href="https://twitter.com/blacklionsoftware"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-12 h-12 bg-white/5 hover:bg-sky-500/10 border border-white/10 hover:border-sky-400/30 rounded-xl flex items-center justify-center transition-all duration-300"
                      >
                        <Twitter className="w-6 h-6 text-gray-400 hover:text-sky-400" />
                      </a>
                      <a
                        href="mailto:<EMAIL>"
                        className="w-12 h-12 bg-white/5 hover:bg-emerald-500/10 border border-white/10 hover:border-emerald-400/30 rounded-xl flex items-center justify-center transition-all duration-300"
                      >
                        <Mail className="w-6 h-6 text-gray-400 hover:text-emerald-400" />
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              {/* Main Form */}
              <div className="lg:col-span-2">
                <Card className="glass border-white/10">
                  <CardContent className="p-8 lg:p-12">
                    <div className="mb-8">
                      <h3 className="text-3xl font-display font-bold text-white mb-4">
                        Tell Us About Your Project
                      </h3>
                      <p className="text-gray-300 font-body text-lg">
                        Fill out the form below and we'll get back to you with a
                        detailed proposal.
                      </p>
                    </div>

                    {isSubmitted ? (
                      <div className="text-center py-16">
                        <div className="w-20 h-20 bg-green-500/10 rounded-full flex items-center justify-center mx-auto mb-6">
                          <CheckCircle className="w-12 h-12 text-green-500" />
                        </div>
                        <h4 className="text-2xl font-display font-bold text-white mb-4">
                          Thank You!
                        </h4>
                        <p className="text-gray-300 font-body text-lg">
                          We've received your message and will get back to you
                          within 24 hours.
                        </p>
                      </div>
                    ) : (
                      <form onSubmit={handleSubmit} className="space-y-6">
                        {/* Basic Information */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label className="block text-sm font-display font-medium text-gray-300 mb-2">
                              Full Name *
                            </label>
                            <Input
                              type="text"
                              name="name"
                              value={formData.name}
                              onChange={handleInputChange}
                              required
                              className="bg-white/5 border-white/20 text-white placeholder-gray-500 focus:border-primary focus:ring-primary h-12 rounded-lg"
                              placeholder="Your full name"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-display font-medium text-gray-300 mb-2">
                              Email Address *
                            </label>
                            <Input
                              type="email"
                              name="email"
                              value={formData.email}
                              onChange={handleInputChange}
                              required
                              className="bg-white/5 border-white/20 text-white placeholder-gray-500 focus:border-primary focus:ring-primary h-12 rounded-lg"
                              placeholder="<EMAIL>"
                            />
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label className="block text-sm font-display font-medium text-gray-300 mb-2">
                              Company
                            </label>
                            <Input
                              type="text"
                              name="company"
                              value={formData.company}
                              onChange={handleInputChange}
                              className="bg-white/5 border-white/20 text-white placeholder-gray-500 focus:border-primary focus:ring-primary h-12 rounded-lg"
                              placeholder="Your company name"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-display font-medium text-gray-300 mb-2">
                              Phone Number
                            </label>
                            <Input
                              type="tel"
                              name="phone"
                              value={formData.phone}
                              onChange={handleInputChange}
                              className="bg-white/5 border-white/20 text-white placeholder-gray-500 focus:border-primary focus:ring-primary h-12 rounded-lg"
                              placeholder="+****************"
                            />
                          </div>
                        </div>

                        {/* Project Details */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                          <div>
                            <label className="block text-sm font-display font-medium text-gray-300 mb-2">
                              Project Type *
                            </label>
                            <select
                              name="project"
                              value={formData.project}
                              onChange={handleInputChange}
                              required
                              className="w-full h-12 px-4 bg-white/5 border outline-none border-white/20 rounded-lg text-white focus:border-primary focus:ring-primary"
                            >
                              <option value="" className="bg-gray-900">
                                Select type
                              </option>
                              {projectTypes.map((type, index) => (
                                <option
                                  key={index}
                                  value={type}
                                  className="bg-gray-900"
                                >
                                  {type}
                                </option>
                              ))}
                            </select>
                          </div>
                          <div>
                            <label className="block text-sm font-display font-medium text-gray-300 mb-2">
                              Budget Range
                            </label>
                            <select
                              name="budget"
                              value={formData.budget}
                              onChange={handleInputChange}
                              className="w-full h-12 px-4 bg-white/5 border outline-none border-white/20 rounded-lg text-white focus:border-primary focus:ring-primary"
                            >
                              <option value="" className="bg-gray-900">
                                Select budget
                              </option>
                              {budgetRanges.map((range, index) => (
                                <option
                                  key={index}
                                  value={range}
                                  className="bg-gray-900"
                                >
                                  {range}
                                </option>
                              ))}
                            </select>
                          </div>
                          <div>
                            <label className="block text-sm font-display font-medium text-gray-300 mb-2">
                              Timeline
                            </label>
                            <select
                              name="timeline"
                              value={formData.timeline}
                              onChange={handleInputChange}
                              className="w-full h-12 px-4 bg-white/5 border outline-none border-white/20 rounded-lg text-white focus:border-primary focus:ring-primary"
                            >
                              <option value="" className="bg-gray-900">
                                Select timeline
                              </option>
                              {timelines.map((timeline, index) => (
                                <option
                                  key={index}
                                  value={timeline}
                                  className="bg-gray-900"
                                >
                                  {timeline}
                                </option>
                              ))}
                            </select>
                          </div>
                        </div>

                        {/* Message */}
                        <div>
                          <label className="block text-sm font-display font-medium text-gray-300 mb-2">
                            Project Description *
                          </label>
                          <Textarea
                            name="message"
                            value={formData.message}
                            onChange={handleInputChange}
                            required
                            rows={6}
                            className="bg-white/5 border-white/20 text-white placeholder-gray-500 focus:border-primary focus:ring-primary resize-none rounded-lg"
                            placeholder="Tell us about your project goals, requirements, target audience, and any specific challenges you're facing..."
                          />
                        </div>

                        {/* Submit */}
                        <div className="pt-6">
                          <Button
                            type="submit"
                            disabled={isSubmitting}
                            className="w-full bg-primary hover:bg-primary/90 text-white font-display font-semibold py-4 px-8 rounded-lg transition-all duration-300 hover:scale-[1.02] flex items-center justify-center space-x-3 disabled:opacity-50 h-14"
                          >
                            {isSubmitting ? (
                              <>
                                <div className="w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                                <span>Sending...</span>
                              </>
                            ) : (
                              <>
                                <Send className="w-5 h-5" />
                                <span>Send Message</span>
                              </>
                            )}
                          </Button>
                          <p className="text-sm text-gray-400 font-body text-center mt-4">
                            We'll respond within 24 hours. Your information is
                            kept secure and confidential.
                          </p>
                        </div>
                      </form>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ContactPage;
