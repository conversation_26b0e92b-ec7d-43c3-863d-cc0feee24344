import { useEffect, useRef } from "react";
import {
  CheckCircle,
  Users,
  Award,
  Globe,
  Zap,
  Lock,
  TrendingUp,
  ArrowUpRight,
} from "lucide-react";

const AdvantageSection = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const cards = entry.target.querySelectorAll(".advantage-card");
            cards.forEach((card, index) => {
              setTimeout(() => {
                card.classList.add("revealed");
              }, index * 150);
            });
          }
        });
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const advantages = [
    {
      icon: Users,
      title: "Global Talent Network",
      description:
        "Access to world-class engineers and specialists across multiple time zones for 24/7 development.",
      highlight: "Expert Team",
      metrics: "500+ Specialists",
      color: "#8b5cf6",
      lightColor: "rgba(139, 92, 246, 0.1)",
      borderColor: "rgba(139, 92, 246, 0.2)",
    },
    {
      icon: Award,
      title: "Proven Track Record",
      description:
        "Delivered 200+ successful projects with 98% client satisfaction and industry recognition.",
      highlight: "Industry Leader",
      metrics: "200+ Projects",
      color: "#06b6d4",
      lightColor: "rgba(6, 182, 212, 0.1)",
      borderColor: "rgba(6, 182, 212, 0.2)",
    },
    {
      icon: Globe,
      title: "Global Reach",
      description:
        "Serving clients across 30+ countries with localized expertise and cultural understanding.",
      highlight: "Worldwide",
      metrics: "30+ Countries",
      color: "#10b981",
      lightColor: "rgba(16, 185, 129, 0.1)",
      borderColor: "rgba(16, 185, 129, 0.2)",
    },
    {
      icon: Zap,
      title: "Rapid Deployment",
      description:
        "Fast project initiation with dedicated teams ready to start within 48 hours.",
      highlight: "Lightning Fast",
      metrics: "48hr Start",
      color: "#f59e0b",
      lightColor: "rgba(245, 158, 11, 0.1)",
      borderColor: "rgba(245, 158, 11, 0.2)",
    },
    {
      icon: Lock,
      title: "Enterprise Security",
      description:
        "Bank-grade security protocols with ISO 27001 certification and compliance standards.",
      highlight: "Secure & Compliant",
      metrics: "ISO 27001",
      color: "#ec4899",
      lightColor: "rgba(236, 72, 153, 0.1)",
      borderColor: "rgba(236, 72, 153, 0.2)",
    },
    {
      icon: TrendingUp,
      title: "Scalable Solutions",
      description:
        "Architecture designed for growth, handling from startup MVPs to enterprise-scale applications.",
      highlight: "Future-Ready",
      metrics: "10x Scalable",
      color: "#6366f1",
      lightColor: "rgba(99, 102, 241, 0.1)",
      borderColor: "rgba(99, 102, 241, 0.2)",
    },
  ];

  return (
    <section
      ref={sectionRef}
      className="py-20 lg:py-28 relative overflow-hidden"
    >
      {/* Enhanced Background with Better Contrast */}
      <div className="absolute inset-0">
        {/* Main Background Gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary via-background to-gray-900/80"></div>

        {/* Subtle Pattern Overlay */}
        <div className="absolute inset-0 opacity-[0.02]">
          <div
            className="w-full h-full"
            style={{
              backgroundImage: `
                   linear-gradient(rgba(255,255,255,0.8) 1px, transparent 1px),
                   linear-gradient(90deg, rgba(255,255,255,0.8) 1px, transparent 1px)
                 `,
              backgroundSize: "40px 40px",
            }}
          ></div>
        </div>

        {/* Ambient Light Effects */}
        <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[800px] h-[400px] bg-primary/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-[600px] h-[300px] bg-purple-500/8 rounded-full blur-3xl"></div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 w-24 h-24 border border-white/20 rounded-2xl rotate-45 animate-float"></div>
        <div
          className="absolute bottom-32 right-16 w-16 h-16 border border-white/10 rounded-full animate-float"
          style={{ animationDelay: "2s" }}
        ></div>
        <div
          className="absolute top-1/2 right-10 w-12 h-12 bg-primary/10 rounded-lg rotate-12 animate-float"
          style={{ animationDelay: "4s" }}
        ></div>
      </div>

      <div className="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8">
        {/* Enhanced Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 glass rounded-full px-4 py-2 mb-6 border border-white/10">
            <CheckCircle className="w-4 h-4 text-primary" />
            <span className="text-xs font-display font-medium text-gray-300 uppercase tracking-wider">
              Your Competitive Edge
            </span>
          </div>

          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-display font-black text-white mb-4 text-balance">
            Your <span className="gradient-text-primary">Unfair Advantage</span>
          </h2>

          <p className="text-gray-400 max-w-2xl mx-auto font-body text-lg leading-relaxed text-pretty">
            We don't just build software—we deliver transformative advantages
            that set you apart from the competition.
          </p>
        </div>

        {/* Enhanced Horizontal Scroll Container */}
        <div className="relative mb-16">
          {/* Improved Fade Effects */}
          <div className="absolute left-0 top-0 bottom-0 w-16 bg-gradient-to-r from-background via-background/80 to-transparent z-10 pointer-events-none"></div>
          <div className="absolute right-0 top-0 bottom-0 w-16 bg-gradient-to-l from-background via-background/80 to-transparent z-10 pointer-events-none"></div>

          {/* Scrollable Cards Container */}
          <div
            ref={scrollContainerRef}
            className="flex gap-6 overflow-x-auto scrollbar-hide py-6 px-8 snap-x snap-mandatory"
            style={{
              scrollbarWidth: "none",
              msOverflowStyle: "none",
              scrollPaddingLeft: "32px",
              scrollPaddingRight: "32px",
            }}
          >
            {advantages.map((advantage, index) => {
              const Icon = advantage.icon;
              return (
                <div
                  key={index}
                  className="advantage-card scroll-reveal flex-shrink-0 w-[340px] snap-center"
                >
                  <div className="glass-strong rounded-3xl p-8 border border-white/10 hover-lift hover-glow group transition-all duration-700 h-full relative overflow-hidden backdrop-blur-xl">
                    {/* Card Background Glow */}
                    <div
                      className="absolute inset-0 opacity-0 group-hover:opacity-100 rounded-3xl transition-all duration-700"
                      style={{
                        background: `radial-gradient(circle at center, ${advantage.lightColor} 0%, transparent 70%)`,
                      }}
                    ></div>

                    {/* Content */}
                    <div className="relative z-10">
                      {/* Icon and Badge Row */}
                      <div className="flex items-start justify-between mb-6">
                        <div
                          className="w-16 h-16 rounded-2xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-all duration-500 shadow-lg"
                          style={{ backgroundColor: advantage.color }}
                        >
                          <Icon className="w-8 h-8 text-white" />
                        </div>

                        <div
                          className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-display font-semibold text-white border backdrop-blur-sm"
                          style={{
                            backgroundColor: advantage.lightColor,
                            borderColor: advantage.borderColor,
                          }}
                        >
                          {advantage.highlight}
                        </div>
                      </div>

                      {/* Title */}
                      <h3 className="font-display font-bold text-white mb-4 group-hover:text-white transition-colors text-xl leading-tight">
                        {advantage.title}
                      </h3>

                      {/* Description */}
                      <p className="text-gray-300 font-body leading-relaxed mb-6 text-sm text-pretty">
                        {advantage.description}
                      </p>

                      {/* Footer */}
                      <div className="flex items-center justify-between pt-4 border-t border-white/10">
                        <div className="font-display font-bold text-white text-sm">
                          {advantage.metrics}
                        </div>
                        <div
                          className="opacity-0 group-hover:opacity-100 transition-all duration-300 w-8 h-8 rounded-lg flex items-center justify-center"
                          style={{ backgroundColor: advantage.lightColor }}
                        >
                          <ArrowUpRight
                            className="w-4 h-4"
                            style={{ color: advantage.color }}
                          />
                        </div>
                      </div>
                    </div>

                    {/* Enhanced Shimmer Effect */}
                    <div className="absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-700">
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-shimmer rounded-3xl"></div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Enhanced Mobile Hint */}
          <div className="flex justify-center mt-6 md:hidden">
            <div className="flex items-center gap-3 text-gray-500 text-sm font-body px-4 py-2 glass rounded-full border border-white/10">
              <div className="flex gap-1">
                <div className="w-1.5 h-1.5 bg-primary rounded-full animate-pulse"></div>
                <div
                  className="w-1.5 h-1.5 bg-primary/60 rounded-full animate-pulse"
                  style={{ animationDelay: "0.3s" }}
                ></div>
                <div
                  className="w-1.5 h-1.5 bg-primary/40 rounded-full animate-pulse"
                  style={{ animationDelay: "0.6s" }}
                ></div>
              </div>
              <span>Swipe to explore advantages</span>
            </div>
          </div>
        </div>

        {/* Enhanced Stats Section */}
        <div className="glass-strong rounded-3xl p-8 border border-white/10 mb-12 relative overflow-hidden backdrop-blur-xl">
          {/* Stats Background Effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-primary/5 rounded-3xl"></div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 text-center relative z-10">
            {[
              { number: "500+", label: "Expert Engineers", delay: "0s" },
              { number: "98%", label: "Client Satisfaction", delay: "0.1s" },
              { number: "30+", label: "Countries Served", delay: "0.2s" },
              { number: "24/7", label: "Global Support", delay: "0.3s" },
            ].map((stat, index) => (
              <div
                key={index}
                className="group"
                style={{ animationDelay: stat.delay }}
              >
                <div className="font-display font-black text-4xl lg:text-5xl text-[rgba(122,71,240,1)] mb-2 group-hover:text-primary transition-colors duration-300">
                  {stat.number}
                </div>
                <div className="text-gray-400 font-body text-sm uppercase tracking-wide">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Enhanced CTA Section */}
        <div className="text-center">
          <div className="glass-strong rounded-3xl p-8 border border-white/10 max-w-2xl mx-auto relative overflow-hidden backdrop-blur-xl">
            {/* CTA Background Glow */}
            <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-transparent to-purple-500/10 rounded-3xl"></div>

            <div className="relative z-10">
              <h3 className="font-display font-bold text-white mb-3 text-2xl">
                Ready to Gain Your Advantage?
              </h3>
              <p className="text-gray-300 font-body mb-6 text-lg leading-relaxed text-pretty">
                Let's discuss how our unique advantages can accelerate your
                success and transform your business.
              </p>

              <button
                onClick={() => {
                  const contactSection = document.getElementById("contact");
                  if (contactSection) {
                    contactSection.scrollIntoView({ behavior: "smooth" });
                  }
                }}
                className="btn-modern inline-flex items-center gap-3 text-base px-8 py-4 hover:scale-105 transition-all duration-300"
              >
                Get Started Today
                <TrendingUp className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AdvantageSection;
