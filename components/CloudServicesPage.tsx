import { useEffect } from "react";
import { But<PERSON> } from "./ui/button";
import {
  Cloud,
  GitBranch,
  Server,
  Shield,
  Monitor,
  Layers,
  ArrowRight,
  CheckCircle,
  Settings,
  Rocket,
  Database,
  Activity,
} from "lucide-react";

const CloudServicesPage = () => {
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("revealed");
          }
        });
      },
      { threshold: 0.1 }
    );

    const elements = document.querySelectorAll(".scroll-reveal");
    elements.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  const handleConnectClick = () => {
    const contactSection = document.getElementById("contact");
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: "smooth" });
    }
  };

  const platformOfferings = [
    { name: "AWS", desc: "Secure, scalable cloud infrastructure", logo: "☁️" },
    {
      name: "Azure",
      desc: "End-to-end cloud suite for building and managing applications",
      logo: "🔷",
    },
    {
      name: "Google Cloud",
      desc: "Adaptive infrastructure with on-demand scalability",
      logo: "🌐",
    },
    {
      name: "Salesforce",
      desc: "Sales, marketing, and customer automation in the cloud",
      logo: "⚡",
    },
    {
      name: "Appian",
      desc: "Low-code AI automation for business processes",
      logo: "🔧",
    },
    {
      name: "Databricks",
      desc: "Unified platform for AI data processing and management",
      logo: "📊",
    },
    {
      name: "Symbox",
      desc: "Process optimization and agile development",
      logo: "⚙️",
    },
    {
      name: "ServiceNow",
      desc: "Centralized ITSM and workflow automation",
      logo: "🔄",
    },
    {
      name: "Microsoft Dynamics 365",
      desc: "AI-powered CRM and ERP integration",
      logo: "💼",
    },
    {
      name: "SharePoint",
      desc: "Streamlined internal collaboration & integration",
      logo: "📁",
    },
    {
      name: "Micro Focus",
      desc: "Enterprise-grade data organization and security",
      logo: "🔒",
    },
    {
      name: "Sitecore",
      desc: "Omnichannel customer experience platform",
      logo: "🎯",
    },
  ];

  return (
    <div className="min-h-screen bg-background pt-20">
      {/* Header Section */}
      <section className="relative py-12 sm:py-16 lg:py-20 gradient-hero overflow-hidden">
        {/* Background Image */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-55"
          style={{
            backgroundImage:
              "url(https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2672&q=80)",
          }}
        ></div>

        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/75 via-black/55 to-black/75"></div>

        <div className="absolute inset-0 opacity-30">
          <div className="particles">
            {[...Array(50)].map((_, i) => (
              <div
                key={i}
                className="particle"
                style={{
                  left: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 15}s`,
                  animationDuration: `${15 + Math.random() * 10}s`,
                }}
              />
            ))}
          </div>
        </div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            <div className="inline-flex items-center space-x-2 bg-primary/10 border border-primary/20 rounded-full px-6 py-3 mb-8">
              <Cloud className="w-5 h-5 text-primary" />
              <span className="font-display font-medium text-primary">
                Cloud Services
              </span>
            </div>

            <h1 className="font-display font-black mb-6 gradient-text-primary">
              Cloud Services
            </h1>

            <p className="font-body text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              Navigate complex digital initiatives with confidence, propelling
              your journey towards innovation and growth through our
              comprehensive cloud solutions and DevOps services.
            </p>

            <Button
              onClick={handleConnectClick}
              className="btn-modern inline-flex items-center space-x-2"
            >
              <span>Explore Our Cloud Services</span>
              <ArrowRight className="w-5 h-5" />
            </Button>
          </div>
        </div>
      </section>

      {/* Cloud Solutions Section */}
      <section id="cloud-solutions" className="py-24 gradient-section">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          {/* Section Header */}
          <div className="text-center max-w-4xl mx-auto mb-16 scroll-reveal">
            <div className="flex items-center justify-center space-x-3 mb-6">
              <div className="w-16 h-16 bg-gradient-to-br from-primary to-cyan-600 rounded-2xl flex items-center justify-center">
                <Cloud className="w-8 h-8 text-white" />
              </div>
              <div>
                <h2 className="font-display font-bold text-4xl text-white text-left">
                  Cloud Solutions
                </h2>
                <p className="font-body text-gray-400 text-left">
                  Enterprise cloud infrastructure
                </p>
              </div>
            </div>

            <h3 className="font-display font-semibold text-3xl text-white mb-6">
              Navigate complex digital initiatives with confidence, propelling
              your journey towards innovation and growth.
            </h3>

            <p className="font-body text-xl text-gray-300 mb-8 leading-relaxed">
              As a strategic cloud development partner, Black Lion helps
              businesses unlock agility, scalability, and innovation by
              integrating with top enterprise platforms and tools.
            </p>

            <Button
              onClick={handleConnectClick}
              className="btn-modern text-lg px-8 py-4"
            >
              Let's Connect
            </Button>
          </div>

          {/* Platform Offerings */}
          <div className="scroll-reveal">
            <h4 className="font-display font-bold text-2xl text-white text-center mb-8">
              Platform Offerings
            </h4>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {platformOfferings.map((platform, index) => (
                <div
                  key={index}
                  className="glass-strong rounded-2xl p-6 text-center hover-glow hover-lift"
                >
                  <div className="text-4xl mb-4">{platform.logo}</div>
                  <h5 className="font-display font-bold text-lg text-white mb-3">
                    {platform.name}
                  </h5>
                  <p className="font-body text-gray-400 text-sm leading-relaxed">
                    {platform.desc}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* CTA */}
          <div className="text-center mt-16 scroll-reveal">
            <p className="font-body text-xl text-gray-300 mb-6">
              Ready to modernize your cloud infrastructure?
            </p>
            <Button
              onClick={handleConnectClick}
              className="btn-modern text-lg px-8 py-4"
            >
              Let's Connect
            </Button>
          </div>
        </div>
      </section>

      {/* DevOps Services Section */}
      <section id="devops-services" className="py-24 bg-gray-950/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          {/* Section Header */}
          <div className="text-center max-w-4xl mx-auto mb-16 scroll-reveal">
            <div className="flex items-center justify-center space-x-3 mb-6">
              <div className="w-16 h-16 bg-gradient-to-br from-green-600 to-blue-600 rounded-2xl flex items-center justify-center">
                <GitBranch className="w-8 h-8 text-white" />
              </div>
              <div>
                <h2 className="font-display font-bold text-4xl text-white text-left">
                  DevOps Services
                </h2>
                <p className="font-body text-gray-400 text-left">
                  Delivery transformation solutions
                </p>
              </div>
            </div>

            <h3 className="font-display font-semibold text-3xl text-white mb-6">
              Ensuring Business Agility Through Delivery Transformation
            </h3>

            <p className="font-body text-xl text-gray-300 mb-8 leading-relaxed">
              At Black Lion, we accelerate software delivery without
              compromising quality. By unifying development and operations, we
              help businesses innovate faster and reduce deployment risks.
            </p>

            <Button
              onClick={handleConnectClick}
              className="btn-modern text-lg px-8 py-4"
            >
              Contact us today to transform your software delivery lifecycle
            </Button>
          </div>

          {/* Core DevOps Services */}
          <div className="mb-16 scroll-reveal">
            <h4 className="font-display font-bold text-2xl text-white text-center mb-8">
              Core DevOps Services
            </h4>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  title: "CI/CD",
                  desc: "Automate builds, testing, and deployment across environments for faster, reliable releases.",
                  icon: Rocket,
                },
                {
                  title: "Monitoring & Logging",
                  desc: "Real-time visibility for improved performance and proactive issue resolution.",
                  icon: Monitor,
                },
                {
                  title: "Service-Oriented Architecture",
                  desc: "Microservice-based scalable development for flexible, maintainable systems.",
                  icon: Layers,
                },
                {
                  title: "Virtualization",
                  desc: "Containerization with Docker/Kubernetes for enhanced agility and scalability.",
                  icon: Server,
                },
                {
                  title: "Infrastructure as Code (IaC)",
                  desc: "Automated, scalable infrastructure management through code-based provisioning.",
                  icon: Settings,
                },
              ].map((service, index) => (
                <div
                  key={index}
                  className="glass-strong rounded-2xl p-8 hover-glow hover-lift"
                >
                  <div className="w-14 h-14 bg-gradient-to-br from-green-600 to-blue-600 rounded-xl flex items-center justify-center mb-6">
                    <service.icon className="w-7 h-7 text-white" />
                  </div>
                  <h5 className="font-display font-bold text-xl text-white mb-4">
                    {service.title}
                  </h5>
                  <p className="font-body text-gray-300 leading-relaxed">
                    {service.desc}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Advanced Models */}
          <div className="scroll-reveal">
            <h4 className="font-display font-bold text-2xl text-white text-center mb-8">
              Advanced Models We Support
            </h4>
            <div className="grid md:grid-cols-2 gap-8">
              {[
                {
                  title: "DevSecOps",
                  desc: "Integrated security within DevOps pipelines for secure, compliant software delivery.",
                  icon: Shield,
                  features: [
                    "Security automation",
                    "Compliance monitoring",
                    "Vulnerability scanning",
                    "Risk assessment",
                  ],
                },
                {
                  title: "MLOps",
                  desc: "Intelligent monitoring for ML model lifecycle management and deployment optimization.",
                  icon: Activity,
                  features: [
                    "Model versioning",
                    "Automated retraining",
                    "Performance monitoring",
                    "A/B testing",
                  ],
                },
                {
                  title: "ModelOps",
                  desc: "Enterprise-grade AI/ML deployment and compliance for scalable machine learning operations.",
                  icon: Database,
                  features: [
                    "Model governance",
                    "Regulatory compliance",
                    "Risk management",
                    "Audit trails",
                  ],
                },
                {
                  title: "GitOps",
                  desc: "Version-controlled infrastructure automation using Git as the single source of truth.",
                  icon: GitBranch,
                  features: [
                    "Git-based workflows",
                    "Automated deployments",
                    "Configuration drift detection",
                    "Rollback capabilities",
                  ],
                },
              ].map((model, index) => (
                <div
                  key={index}
                  className="glass-strong rounded-2xl p-8 hover-glow hover-lift"
                >
                  <div className="flex items-center space-x-4 mb-6">
                    <div className="w-14 h-14 bg-gradient-to-br from-green-600 to-blue-600 rounded-xl flex items-center justify-center">
                      <model.icon className="w-7 h-7 text-white" />
                    </div>
                    <div>
                      <h5 className="font-display font-bold text-xl text-white">
                        {model.title}
                      </h5>
                      <p className="font-body text-gray-400 text-sm">
                        Advanced deployment model
                      </p>
                    </div>
                  </div>
                  <p className="font-body text-gray-300 mb-6 leading-relaxed">
                    {model.desc}
                  </p>
                  <div className="grid grid-cols-2 gap-2">
                    {model.features.map((feature, idx) => (
                      <div key={idx} className="flex items-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-primary flex-shrink-0" />
                        <span className="font-body text-gray-400 text-sm">
                          {feature}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* CTA */}
          <div className="text-center mt-16 scroll-reveal">
            <p className="font-body text-xl text-gray-300 mb-6">
              Ready to accelerate your software delivery?
            </p>
            <Button
              onClick={handleConnectClick}
              className="btn-modern text-lg px-8 py-4"
            >
              Contact us today to transform your software delivery lifecycle
            </Button>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary/10 to-cyan-600/10 border-t border-white/10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-3xl mx-auto scroll-reveal">
            <h2 className="font-display font-bold text-4xl text-white mb-6">
              Ready to Transform Your Cloud Infrastructure?
            </h2>
            <p className="font-body text-xl text-gray-300 mb-8 leading-relaxed">
              Let's discuss how our cloud solutions and DevOps expertise can
              accelerate your digital transformation and drive innovation.
            </p>
            <Button
              onClick={handleConnectClick}
              className="btn-modern text-lg px-8 py-4"
            >
              Start Your Cloud Journey Today
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default CloudServicesPage;
