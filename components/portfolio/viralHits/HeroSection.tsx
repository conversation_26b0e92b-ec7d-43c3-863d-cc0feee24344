// import webMockupImage from "figma:asset/acd7230c9e36740a61405717afde9719a5c7de3a.png";

export function HeroSection() {
  const features = [
    "Real-time Analytics",
    "Multi-platform Tracking",
    "Virality Prediction",
    "Social Media Insights",
    "Performance Metrics",
    "Trend Analysis",
  ];

  return (
    <div
      className="viral-hits-isolated"
      style={{
        all: "unset",
        display: "block",
        fontFamily: '"Inter", sans-serif !important',
        fontSize: "14px !important",
        color: "#1a1a1a !important",
        backgroundColor: "#fafafa !important",
        lineHeight: "1.6 !important",
        fontWeight: "400 !important",
        boxSizing: "border-box",
      }}
    >
      <section className="min-h-screen flex items-center justify-center px-6 py-24 relative overflow-hidden bg-tech-gradient">
        {/* Background Elements */}
        <div
          className="absolute inset-0 bg-gradient-to-br"
          style={{
            background:
              "linear-gradient(to bottom right, rgba(220, 38, 38, 0.05), rgba(6, 182, 212, 0.05))",
          }}
        ></div>
        <div
          className="absolute top-1/4 left-1/4 w-96 h-96 rounded-full blur-3xl animate-pulse-soft"
          style={{ backgroundColor: "rgba(220, 38, 38, 0.1)" }}
        ></div>
        <div
          className="absolute bottom-1/4 right-1/4 w-96 h-96 rounded-full blur-3xl animate-pulse-soft delay-1000"
          style={{ backgroundColor: "rgba(6, 182, 212, 0.1)" }}
        ></div>

        {/* Floating Music Icons */}
        <div className="absolute top-20 left-10 animate-float">
          <div
            className="w-16 h-16 rounded-full flex items-center justify-center"
            style={{ backgroundColor: "rgba(220, 38, 38, 0.2)" }}
          >
            <svg
              width="32"
              height="32"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              style={{ color: "#dc2626" }}
            >
              <path d="M9 18V5l12-2v13" />
              <circle cx="6" cy="18" r="3" />
              <circle cx="18" cy="16" r="3" />
            </svg>
          </div>
        </div>

        <div className="absolute top-32 right-20 animate-float delay-500">
          <div className="w-12 h-12 rounded-full bg-accent/20 flex items-center justify-center">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              className="text-accent"
            >
              <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
            </svg>
          </div>
        </div>

        <div className="absolute bottom-20 left-20 animate-bounce-gentle">
          <div className="w-14 h-14 rounded-full bg-warning/20 flex items-center justify-center">
            <svg
              width="28"
              height="28"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              className="text-warning"
            >
              <path d="M3 18v-6a9 9 0 0 1 18 0v6" />
              <path d="M21 19a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3zM3 19a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2v-3a2 2 0 0 0-2-2H3z" />
            </svg>
          </div>
        </div>

        {/* Data Flow Animation */}
        <div className="absolute top-1/2 left-0 w-full h-1 overflow-hidden">
          <div className="animate-data-flow h-full w-20 bg-gradient-to-r from-transparent via-primary to-transparent"></div>
        </div>

        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center">
            {/* Project Badge */}
            <div
              className="inline-flex items-center gap-3 px-8 py-4 rounded-full mb-8 modern-card border shadow-lg animate-fade-in-up"
              style={{ borderColor: "rgba(220, 38, 38, 0.3)" }}
            >
              <div
                className="w-8 h-8 rounded-full flex items-center justify-center"
                style={{ backgroundColor: "#dc2626" }}
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  className="text-white"
                >
                  <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" />
                </svg>
              </div>
              <span className="text-lg font-medium text-foreground font-inter">
                Music Analytics Platform
              </span>
            </div>

            {/* Main Heading */}
            <h1 className="text-7xl md:text-8xl lg:text-9xl font-bold mb-8 animate-fade-in-up delay-200 font-inter">
              <span className="text-foreground">Track Your</span>
              <br />
              <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                Viral Hits
              </span>
            </h1>

            {/* Project Name */}
            <div className="mb-8 animate-fade-in-up delay-300">
              <div
                className="inline-flex items-center gap-4 px-8 py-4 rounded-full modern-card border shadow-lg"
                style={{ borderColor: "rgba(220, 38, 38, 0.2)" }}
              >
                <div
                  className="w-6 h-6 rounded-full animate-pulse"
                  style={{ backgroundColor: "#dc2626" }}
                ></div>
                <span
                  className="text-2xl font-bold font-inter"
                  style={{ color: "#dc2626" }}
                >
                  VIRAL SONG CHECKER
                </span>
              </div>
            </div>

            {/* Body Text under VIRAL SONG CHECKER */}
            <div className="mb-12 animate-fade-in-up delay-400">
              <p className="text-xl max-w-4xl mx-auto leading-relaxed font-inter text-muted">
                An intelligent analytics platform that tracks song performance
                across social media platforms in real-time. Predict viral
                potential, analyze engagement metrics, and optimize your music
                marketing strategy with data-driven insights.
              </p>
            </div>

            {/* Feature Tags */}
            <div className="flex flex-wrap justify-center gap-4 animate-fade-in-up delay-500 mb-16">
              {features.map((feature, index) => (
                <div
                  key={index}
                  className="px-6 py-3 rounded-full text-white font-medium text-sm font-inter shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                  style={{ backgroundColor: "#dc2626" }}
                  onMouseEnter={(e) =>
                    ((e.target as HTMLElement).style.backgroundColor =
                      "rgba(220, 38, 38, 0.9)")
                  }
                  onMouseLeave={(e) =>
                    ((e.target as HTMLElement).style.backgroundColor =
                      "#dc2626")
                  }
                >
                  {feature}
                </div>
              ))}
            </div>

            {/* Web Mockup */}
            <div className="animate-fade-in-up delay-700 mb-16">
              <div className="max-w-4xl mx-auto">
                <div
                  className="modern-card border rounded-3xl p-6 modern-card-hover"
                  style={{ borderColor: "rgba(220, 38, 38, 0.2)" }}
                >
                  <div
                    className="relative rounded-2xl overflow-hidden shadow-2xl border transform hover:scale-105 transition-transform duration-300"
                    style={{ borderColor: "rgba(220, 38, 38, 0.1)" }}
                  >
                    <img
                      src={"webMockupImage"}
                      alt="Viral Song Checker Search Interface - Search results for Brandz tracks including Glo, Love No Thotties, 3Hunna, and Macaroni Time with options to continue analysis and upgrade for multiple track analysis"
                      className="w-full h-auto"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/5 to-transparent pointer-events-none"></div>
                  </div>

                  {/* Mockup Description */}
                  <div className="mt-6 grid md:grid-cols-3 gap-4">
                    <div
                      className="text-center p-4 rounded-xl border"
                      style={{
                        backgroundColor: "rgba(220, 38, 38, 0.05)",
                        borderColor: "rgba(220, 38, 38, 0.1)",
                      }}
                    >
                      <h6
                        className="font-inter font-bold text-sm mb-1"
                        style={{ color: "#dc2626" }}
                      >
                        Track Search
                      </h6>
                      <p className="font-inter text-xs text-muted">
                        Find any artist's songs instantly
                      </p>
                    </div>
                    <div className="text-center p-4 rounded-xl bg-accent/5 border border-accent/10">
                      <h6 className="font-inter font-bold text-accent text-sm mb-1">
                        Multi-Track Analysis
                      </h6>
                      <p className="font-inter text-xs text-muted">
                        Analyze multiple songs at once
                      </p>
                    </div>
                    <div className="text-center p-4 rounded-xl bg-success/5 border border-success/10">
                      <h6 className="font-inter font-bold text-success text-sm mb-1">
                        Smart Recommendations
                      </h6>
                      <p className="font-inter text-xs text-muted">
                        Get actionable insights
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Metrics Preview */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto animate-fade-in-up delay-800">
              <div className="modern-card rounded-2xl p-6 text-center modern-card-hover">
                <div
                  className="text-3xl font-bold font-mono mb-2"
                  style={{ color: "#dc2626" }}
                >
                  87
                </div>
                <div className="text-sm text-muted font-inter">
                  Virality Score
                </div>
              </div>
              <div className="modern-card rounded-2xl p-6 text-center modern-card-hover">
                <div className="text-3xl font-bold text-accent font-mono mb-2">
                  1.2M
                </div>
                <div className="text-sm text-muted font-inter">
                  Total Streams
                </div>
              </div>
              <div className="modern-card rounded-2xl p-6 text-center modern-card-hover">
                <div className="text-3xl font-bold text-warning font-mono mb-2">
                  150K
                </div>
                <div className="text-sm text-muted font-inter">
                  Social Shares
                </div>
              </div>
              <div className="modern-card rounded-2xl p-6 text-center modern-card-hover">
                <div className="text-3xl font-bold text-success font-mono mb-2">
                  95%
                </div>
                <div className="text-sm text-muted font-inter">
                  Accuracy Rate
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
