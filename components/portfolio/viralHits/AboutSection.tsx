import "../../../styles/globals2.css";

export function AboutSection() {
  const projectStats = [
    { label: "Development Time", value: "8 months", icon: "⏱️" },
    { label: "Team Members", value: "6 people", icon: "👥" },
    { label: "Platforms Tracked", value: "8+", icon: "📱" },
    { label: "Data Points", value: "50M+", icon: "📊" },
  ];

  const keyFeatures = [
    {
      title: "Real-time Data Processing",
      description:
        "Processing millions of data points per hour from multiple social media APIs to provide instant insights.",
    },
    {
      title: "Machine Learning Integration",
      description:
        "Advanced AI algorithms that learn from historical data to improve prediction accuracy over time.",
    },
    {
      title: "Scalable Architecture",
      description:
        "Built to handle massive data volumes with cloud-native infrastructure and microservices architecture.",
    },
    {
      title: "User-Centric Design",
      description:
        "Intuitive dashboard designed specifically for music industry professionals with actionable insights.",
    },
  ];

  return (
    <div
      className="viral-hits-isolated"
      style={{
        all: "unset",
        display: "block",
        fontFamily: '"Inter", sans-serif !important',
        fontSize: "14px !important",
        color: "#1a1a1a !important",
        backgroundColor: "#fafafa !important",
        lineHeight: "1.6 !important",
        fontWeight: "400 !important",
        boxSizing: "border-box",
      }}
    >
      <section className="py-24 px-6 bg-gradient-to-br from-background to-primary/5 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-accent/5 to-primary/5"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-accent/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl"></div>

        <div className="max-w-7xl mx-auto relative z-10">
          {/* Section Header */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-3 px-8 py-4 rounded-full mb-8 modern-card border border-accent/30 shadow-lg">
              <span className="text-lg font-medium text-foreground font-inter">
                About Project
              </span>
            </div>

            <h2 className="text-6xl font-bold mb-8 font-inter text-foreground">
              Viral Song Checker
            </h2>

            <p className="text-xl max-w-4xl mx-auto leading-relaxed font-inter text-muted">
              A comprehensive music analytics platform that empowers artists,
              labels, and marketers to track, analyze, and predict song
              performance across all major digital platforms.
            </p>
          </div>

          {/* Project Overview */}
          <div className="grid lg:grid-cols-2 gap-12 mb-20">
            <div className="modern-card border border-primary/20 rounded-3xl p-8 modern-card-hover">
              <h3 className="text-3xl font-bold font-inter text-foreground mb-6">
                Project Overview
              </h3>
              <div className="space-y-4 text-muted font-inter leading-relaxed">
                <p>
                  Viral Song Checker addresses the critical need for
                  comprehensive music analytics in today's fragmented digital
                  landscape. With music consumption spread across numerous
                  platforms, artists and labels struggled to get a unified view
                  of their song performance.
                </p>
                <p>
                  Our platform aggregates data from Spotify, YouTube, TikTok,
                  Instagram, Twitter, and other major platforms to provide
                  real-time insights and viral prediction capabilities that help
                  optimize marketing strategies.
                </p>
                <p>
                  The result is a powerful analytics tool that has become
                  essential for music industry professionals, providing the
                  data-driven insights needed to succeed in the competitive
                  digital music landscape.
                </p>
              </div>
            </div>

            <div className="modern-card border border-accent/20 rounded-3xl p-8 modern-card-hover">
              <h3 className="text-3xl font-bold font-inter text-foreground mb-6">
                Key Achievements
              </h3>
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 rounded-full bg-success/20 flex items-center justify-center shrink-0">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      className="text-success"
                    >
                      <path d="M9 12l2 2 4-4" />
                      <circle cx="12" cy="12" r="9" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-bold text-foreground font-inter mb-1">
                      95% Prediction Accuracy
                    </h4>
                    <p className="text-sm text-muted font-inter">
                      AI algorithms achieve industry-leading accuracy in viral
                      prediction
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center shrink-0">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      className="text-primary"
                    >
                      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                      <circle cx="9" cy="7" r="4" />
                      <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                      <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-bold text-foreground font-inter mb-1">
                      10,000+ Active Users
                    </h4>
                    <p className="text-sm text-muted font-inter">
                      Artists, labels, and marketers worldwide
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 rounded-full bg-accent/20 flex items-center justify-center shrink-0">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      className="text-accent"
                    >
                      <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-bold text-foreground font-inter mb-1">
                      50M+ Data Points Daily
                    </h4>
                    <p className="text-sm text-muted font-inter">
                      Real-time processing across all platforms
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Project Statistics */}
          <div className="mb-20">
            <h3 className="text-4xl font-bold font-inter text-foreground text-center mb-12">
              Project Statistics
            </h3>

            <div className="grid md:grid-cols-4 gap-6">
              {projectStats.map((stat, index) => (
                <div
                  key={index}
                  className="modern-card border border-primary/20 rounded-3xl p-8 text-center modern-card-hover animate-fade-in-up"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-3xl font-bold text-primary font-mono mb-2">
                    {stat.value}
                  </div>
                  <div className="text-sm text-muted font-inter">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Key Features */}
          <div>
            <h3 className="text-4xl font-bold font-inter text-foreground text-center mb-12">
              Technical Highlights
            </h3>

            <div className="grid md:grid-cols-2 gap-8">
              {keyFeatures.map((feature, index) => (
                <div
                  key={index}
                  className="modern-card border border-accent/20 rounded-3xl p-8 modern-card-hover animate-fade-in-up"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 rounded-2xl bg-accent/20 flex items-center justify-center shrink-0">
                      <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        className="text-accent"
                      >
                        <circle cx="12" cy="12" r="3" />
                        <path d="M12 1v6m0 6v6" />
                        <path d="m15.5 3.5-1.5 1.5" />
                        <path d="m10.5 14.5-1.5 1.5" />
                        <path d="m21.5 10.5-1.5 1.5" />
                        <path d="m3.5 8.5 1.5 1.5" />
                        <path d="m15.5 20.5-1.5-1.5" />
                        <path d="m10.5 9.5-1.5-1.5" />
                        <path d="m21.5 13.5-1.5-1.5" />
                        <path d="m3.5 15.5 1.5-1.5" />
                      </svg>
                    </div>

                    <div>
                      <h4 className="text-xl font-bold font-inter text-foreground mb-3">
                        {feature.title}
                      </h4>
                      <p className="font-inter text-muted leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
