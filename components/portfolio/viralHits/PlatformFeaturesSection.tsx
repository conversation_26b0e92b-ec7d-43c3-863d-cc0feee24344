import "../../../styles/globals2.css";

export function PlatformFeaturesSection() {
  const features = [
    {
      icon: (
        <svg
          width="40"
          height="40"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <circle cx="12" cy="12" r="3" />
          <path d="M12 1v6m0 6v6" />
          <path d="m15.5 3.5-1.5 1.5" />
          <path d="m10.5 14.5-1.5 1.5" />
          <path d="m21.5 10.5-1.5 1.5" />
          <path d="m3.5 8.5 1.5 1.5" />
          <path d="m15.5 20.5-1.5-1.5" />
          <path d="m10.5 9.5-1.5-1.5" />
          <path d="m21.5 13.5-1.5-1.5" />
          <path d="m3.5 15.5 1.5-1.5" />
        </svg>
      ),
      title: "Real-time Multi-Platform Tracking",
      description:
        "Monitor song performance across Spotify, YouTube, TikTok, Instagram, and more with live updates every 15 minutes.",
      metrics: ["15min", "Updates", "8+", "Platforms"],
      color: "primary",
    },
    {
      icon: (
        <svg
          width="40"
          height="40"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" />
          <circle cx="12" cy="12" r="4" />
        </svg>
      ),
      title: "AI Virality Prediction",
      description:
        "Advanced machine learning algorithms analyze engagement patterns to predict viral potential with 95% accuracy.",
      metrics: ["95%", "Accuracy", "24h", "Prediction"],
      color: "accent",
    },
    {
      icon: (
        <svg
          width="40"
          height="40"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <path d="M3 3v18h18" />
          <path d="M18.7 8a3 3 0 0 0-5.4 0 3 3 0 0 0-5.4 0 3 3 0 0 0-5.4 0" />
        </svg>
      ),
      title: "Comprehensive Analytics Dashboard",
      description:
        "Beautiful, intuitive dashboard displaying streams, shares, engagement rates, and demographic breakdowns.",
      metrics: ["50+", "Metrics", "Real-time", "Data"],
      color: "success",
    },
    {
      icon: (
        <svg
          width="40"
          height="40"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
          <circle cx="9" cy="7" r="4" />
          <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
          <path d="M16 3.13a4 4 0 0 1 0 7.75" />
        </svg>
      ),
      title: "Audience Demographics Insights",
      description:
        "Detailed breakdown of listener demographics including age, location, gender, and listening habits.",
      metrics: ["25+", "Demographics", "Global", "Coverage"],
      color: "warning",
    },
    {
      icon: (
        <svg
          width="40"
          height="40"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" />
        </svg>
      ),
      title: "Smart Recommendations",
      description:
        "Data-driven suggestions for optimal posting times, target regions, and marketing strategies to maximize reach.",
      metrics: ["AI-powered", "Insights", "Actionable", "Tips"],
      color: "primary",
    },
    {
      icon: (
        <svg
          width="40"
          height="40"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
          <path d="M4 10l8-8 8 8" />
          <path d="M12 2v20" />
        </svg>
      ),
      title: "Trend Analysis & Forecasting",
      description:
        "Identify emerging trends, seasonal patterns, and predict future performance based on historical data.",
      metrics: ["Trend", "Detection", "Future", "Forecast"],
      color: "accent",
    },
  ];

  return (
    <div
      className="viral-hits-isolated"
      style={{
        all: "unset",
        display: "block",
        fontFamily: '"Inter", sans-serif !important',
        fontSize: "14px !important",
        color: "#1a1a1a !important",
        backgroundColor: "#fafafa !important",
        lineHeight: "1.6 !important",
        fontWeight: "400 !important",
        boxSizing: "border-box",
      }}
    >
      <section className="py-24 px-6 bg-gradient-to-br from-accent/5 to-primary/5 relative overflow-hidden">
        {/* Background Elements */}
        <div
          className="absolute inset-0 bg-gradient-to-br"
          style={{
            background:
              "linear-gradient(to bottom right, rgba(220, 38, 38, 0.05), rgba(6, 182, 212, 0.05))",
          }}
        ></div>
        <div
          className="absolute top-1/4 right-1/4 w-96 h-96 rounded-full blur-3xl animate-pulse-soft"
          style={{ backgroundColor: "rgba(6, 182, 212, 0.1)" }}
        ></div>
        <div
          className="absolute bottom-1/4 left-1/4 w-96 h-96 rounded-full blur-3xl animate-pulse-soft delay-1000"
          style={{ backgroundColor: "rgba(220, 38, 38, 0.1)" }}
        ></div>

        <div className="max-w-7xl mx-auto relative z-10">
          {/* Section Header */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-3 px-8 py-4 rounded-full mb-8 modern-card border border-accent/30 shadow-lg">
              <span className="text-lg font-medium text-foreground font-inter">
                Platform Features
              </span>
            </div>

            <h2 className="text-6xl font-bold mb-8 font-inter text-foreground">
              Powerful Analytics Tools
            </h2>

            <p className="text-xl max-w-4xl mx-auto leading-relaxed font-inter text-muted">
              Comprehensive suite of tools designed to track, analyze, and
              predict music performance across all major social media and
              streaming platforms.
            </p>
          </div>

          {/* Features Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="modern-card border rounded-3xl p-8 modern-card-hover animate-fade-in-up group"
                style={{
                  borderColor: "rgba(220, 38, 38, 0.2)",
                  animationDelay: `${index * 0.1}s`,
                }}
              >
                {/* Feature Icon */}
                <div
                  className={`w-20 h-20 mb-6 rounded-2xl bg-${feature.color} flex items-center justify-center text-white shadow-lg group-hover:scale-110 transition-transform duration-300`}
                >
                  {feature.icon}
                </div>

                {/* Feature Content */}
                <h3 className="text-2xl font-bold font-inter text-foreground mb-4">
                  {feature.title}
                </h3>
                <p className="font-inter text-muted leading-relaxed mb-6">
                  {feature.description}
                </p>

                {/* Feature Metrics */}
                <div className="grid grid-cols-2 gap-3">
                  {feature.metrics.map((metric, metricIndex) => (
                    <div
                      key={metricIndex}
                      className={`px-3 py-2 rounded-lg bg-${feature.color}/10 text-center`}
                    >
                      <div
                        className={`text-sm font-bold text-${feature.color} font-mono`}
                      >
                        {metric}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* Bottom CTA Section */}
          <div className="mt-20 text-center">
            <div
              className="modern-card border rounded-3xl p-12 modern-card-hover"
              style={{ borderColor: "rgba(220, 38, 38, 0.2)" }}
            >
              <div className="max-w-3xl mx-auto">
                <h3 className="text-4xl font-bold font-inter text-foreground mb-6">
                  Ready to Track Your Next Hit?
                </h3>
                <p className="text-xl font-inter text-muted mb-8 leading-relaxed">
                  Join thousands of artists, labels, and marketers who trust
                  Viral Song Checker to optimize their music marketing
                  strategies.
                </p>

                <div className="flex flex-wrap justify-center gap-4">
                  <div
                    className="px-8 py-4 text-white rounded-full font-inter font-medium transition-colors cursor-pointer"
                    style={{ backgroundColor: "#dc2626" }}
                    onMouseEnter={(e) =>
                      ((e.target as HTMLElement).style.backgroundColor =
                        "rgba(220, 38, 38, 0.9)")
                    }
                    onMouseLeave={(e) =>
                      ((e.target as HTMLElement).style.backgroundColor =
                        "#dc2626")
                    }
                  >
                    Start Free Trial
                  </div>
                  <div
                    className="px-8 py-4 border rounded-full font-inter font-medium transition-colors cursor-pointer"
                    style={{ borderColor: "#dc2626", color: "#dc2626" }}
                    onMouseEnter={(e) =>
                      ((e.target as HTMLElement).style.backgroundColor =
                        "rgba(220, 38, 38, 0.05)")
                    }
                    onMouseLeave={(e) =>
                      ((e.target as HTMLElement).style.backgroundColor =
                        "transparent")
                    }
                  >
                    View Live Demo
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
