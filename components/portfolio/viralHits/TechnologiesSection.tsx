export function TechnologiesSection() {
  const technologies = [
    {
      name: "React & Next.js",
      category: "Frontend",
      description:
        "Modern React framework with SSR capabilities for fast, interactive user interfaces and optimal SEO.",
      icon: (
        <svg
          width="40"
          height="40"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <path d="M12 2L2 7l10 5 10-5-10-5z" />
          <path d="m2 17 10 5 10-5" />
          <path d="m2 12 10 5 10-5" />
        </svg>
      ),
      color: "accent",
    },
    {
      name: "Node.js & Express",
      category: "Backend",
      description:
        "Scalable server-side JavaScript runtime with Express framework for handling API requests and real-time data processing.",
      icon: (
        <svg
          width="40"
          height="40"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" />
          <polyline points="3.27,6.96 12,12.01 20.73,6.96" />
          <line x1="12" y1="22.08" x2="12" y2="12" />
        </svg>
      ),
      color: "success",
    },
    {
      name: "Python & TensorFlow",
      category: "Machine Learning",
      description:
        "Advanced ML algorithms for viral prediction using TensorFlow and scikit-learn for data analysis and pattern recognition.",
      icon: (
        <svg
          width="40"
          height="40"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <circle cx="12" cy="12" r="3" />
          <path d="M12 1v6m0 6v6" />
          <path d="m15.5 3.5-1.5 1.5" />
          <path d="m10.5 14.5-1.5 1.5" />
          <path d="m21.5 10.5-1.5 1.5" />
          <path d="m3.5 8.5 1.5 1.5" />
          <path d="m15.5 20.5-1.5-1.5" />
          <path d="m10.5 9.5-1.5-1.5" />
          <path d="m21.5 13.5-1.5-1.5" />
          <path d="m3.5 15.5 1.5-1.5" />
        </svg>
      ),
      color: "warning",
    },
    {
      name: "MongoDB & Redis",
      category: "Database",
      description:
        "NoSQL database for flexible data storage with Redis for high-performance caching and real-time data processing.",
      icon: (
        <svg
          width="40"
          height="40"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
          <polyline points="14,2 14,8 20,8" />
          <line x1="16" y1="13" x2="8" y2="13" />
          <line x1="16" y1="17" x2="8" y2="17" />
          <polyline points="10,9 9,9 8,9" />
        </svg>
      ),
      color: "primary",
    },
    {
      name: "AWS Cloud Services",
      category: "Infrastructure",
      description:
        "Scalable cloud infrastructure using EC2, Lambda, RDS, and S3 for reliable, high-performance data processing.",
      icon: (
        <svg
          width="40"
          height="40"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z" />
        </svg>
      ),
      color: "secondary",
    },
    {
      name: "Social Media APIs",
      category: "Integrations",
      description:
        "Direct integrations with Spotify Web API, YouTube Data API, TikTok API, Instagram Graph API, and Twitter API v2.",
      icon: (
        <svg
          width="40"
          height="40"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" />
          <rect x="8" y="2" width="8" height="4" rx="1" ry="1" />
          <path d="M12 11v5" />
          <path d="m9 14 3 3 3-3" />
        </svg>
      ),
      color: "destructive",
    },
  ];

  return (
    <section className="py-24 px-6 bg-gradient-to-br from-primary/5 to-accent/5 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-accent/5 to-primary/5"></div>
      <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl animate-pulse-soft"></div>
      <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-pulse-soft delay-1000"></div>

      {/* Tech Icons Floating Animation */}
      <div className="absolute top-20 right-20 animate-float">
        <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            className="text-primary"
          >
            <polyline points="16 18 22 12 16 6" />
            <polyline points="8 6 2 12 8 18" />
          </svg>
        </div>
      </div>

      <div className="absolute bottom-32 left-16 animate-float delay-700">
        <div className="w-14 h-14 rounded-full bg-accent/20 flex items-center justify-center">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            className="text-accent"
          >
            <path d="M12 2L2 7l10 5 10-5-10-5z" />
            <path d="m2 17 10 5 10-5" />
            <path d="m2 12 10 5 10-5" />
          </svg>
        </div>
      </div>

      <div className="max-w-7xl mx-auto relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-3 px-8 py-4 rounded-full mb-8 modern-card border border-primary/30 shadow-lg">
            <span className="text-lg font-medium text-foreground font-inter">
              Tech Stack
            </span>
          </div>

          <h2 className="text-6xl font-bold mb-8 font-inter text-foreground">
            Technologies Used
          </h2>

          <p className="text-xl max-w-4xl mx-auto leading-relaxed font-inter text-muted">
            Cutting-edge technology stack designed for high-performance data
            processing, real-time analytics, and scalable machine learning
            capabilities.
          </p>
        </div>

        {/* Technologies Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {technologies.map((tech, index) => (
            <div
              key={index}
              className="modern-card border border-primary/20 rounded-3xl p-8 modern-card-hover animate-fade-in-up group"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {/* Tech Icon & Category */}
              <div className="flex items-center justify-between mb-6">
                <div
                  className={`w-16 h-16 rounded-2xl bg-${tech.color} flex items-center justify-center text-white shadow-lg group-hover:scale-110 transition-transform duration-300`}
                >
                  {tech.icon}
                </div>
                <div
                  className={`px-3 py-1 rounded-full bg-${tech.color}/10 text-${tech.color} text-sm font-medium font-inter`}
                >
                  {tech.category}
                </div>
              </div>

              {/* Tech Content */}
              <h3 className="text-2xl font-bold font-inter text-foreground mb-4">
                {tech.name}
              </h3>
              <p className="font-inter text-muted leading-relaxed">
                {tech.description}
              </p>
            </div>
          ))}
        </div>

        {/* Architecture Overview */}
        <div className="mt-20">
          <h3 className="text-4xl font-bold font-inter text-foreground text-center mb-12">
            System Architecture
          </h3>

          <div className="modern-card border border-accent/20 rounded-3xl p-12 modern-card-hover">
            <div className="grid lg:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-20 h-20 mx-auto mb-6 rounded-2xl bg-accent flex items-center justify-center text-white shadow-lg">
                  <svg
                    width="40"
                    height="40"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                  >
                    <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                  </svg>
                </div>
                <h4 className="text-xl font-bold font-inter text-foreground mb-3">
                  Data Collection
                </h4>
                <p className="font-inter text-muted text-sm leading-relaxed">
                  Automated data ingestion from 8+ social media platforms using
                  official APIs with rate limiting and error handling.
                </p>
              </div>

              <div className="text-center">
                <div className="w-20 h-20 mx-auto mb-6 rounded-2xl bg-primary flex items-center justify-center text-white shadow-lg">
                  <svg
                    width="40"
                    height="40"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                  >
                    <circle cx="12" cy="12" r="3" />
                    <path d="M12 1v6m0 6v6" />
                  </svg>
                </div>
                <h4 className="text-xl font-bold font-inter text-foreground mb-3">
                  Processing Engine
                </h4>
                <p className="font-inter text-muted text-sm leading-relaxed">
                  Real-time data processing pipeline with ML algorithms for
                  pattern recognition and viral prediction analysis.
                </p>
              </div>

              <div className="text-center">
                <div className="w-20 h-20 mx-auto mb-6 rounded-2xl bg-success flex items-center justify-center text-white shadow-lg">
                  <svg
                    width="40"
                    height="40"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                  >
                    <path d="M3 3v18h18" />
                    <path d="M18.7 8a3 3 0 0 0-5.4 0 3 3 0 0 0-5.4 0 3 3 0 0 0-5.4 0" />
                  </svg>
                </div>
                <h4 className="text-xl font-bold font-inter text-foreground mb-3">
                  Analytics Dashboard
                </h4>
                <p className="font-inter text-muted text-sm leading-relaxed">
                  Interactive dashboard with real-time charts, predictions, and
                  actionable insights for music industry professionals.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
