import "../../../styles/globals2.css";

export function ChallengesSection() {
  const challenges = [
    {
      icon: (
        <svg
          width="32"
          height="32"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <path d="M3 3v18h18" />
          <path d="M18.7 8a3 3 0 0 0-5.4 0 3 3 0 0 0-5.4 0 3 3 0 0 0-5.4 0" />
        </svg>
      ),
      title: "Fragmented Data Sources",
      description:
        "Music performance data scattered across multiple platforms (Spotify, YouTube, TikTok, Instagram) making comprehensive analysis difficult.",
    },
    {
      icon: (
        <svg
          width="32"
          height="32"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <circle cx="12" cy="12" r="3" />
          <path d="M12 1v6m0 6v6" />
          <path d="m15.5 3.5-1.5 1.5" />
          <path d="m10.5 14.5-1.5 1.5" />
          <path d="m21.5 10.5-1.5 1.5" />
          <path d="m3.5 8.5 1.5 1.5" />
          <path d="m15.5 20.5-1.5-1.5" />
          <path d="m10.5 9.5-1.5-1.5" />
          <path d="m21.5 13.5-1.5-1.5" />
          <path d="m3.5 15.5 1.5-1.5" />
        </svg>
      ),
      title: "Real-time Tracking Complexity",
      description:
        "Difficulty in monitoring song performance across platforms simultaneously with real-time updates and accurate metrics.",
    },
    {
      icon: (
        <svg
          width="32"
          height="32"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <path d="M9 12l2 2 4-4" />
          <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3" />
          <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3" />
          <path d="M13 12h1c1 0 1 1 2 1s1-1 2-1h1" />
          <path d="M11 12h-1c-1 0-1 1-2 1s-1-1-2-1H5" />
        </svg>
      ),
      title: "Viral Prediction Accuracy",
      description:
        "Lack of reliable algorithms to predict which songs will go viral based on early performance indicators and engagement patterns.",
    },
    {
      icon: (
        <svg
          width="32"
          height="32"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <path d="M8 6v6a3 3 0 0 0 3 3h2a3 3 0 0 0 3-3V6" />
          <path d="M8 6a3 3 0 0 1 3-3h2a3 3 0 0 1 3 3" />
          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
          <path d="M5 15h14" />
        </svg>
      ),
      title: "Actionable Insights Gap",
      description:
        "Raw analytics data without clear recommendations on how to optimize marketing strategies and improve song performance.",
    },
  ];

  const solutions = [
    {
      icon: (
        <svg
          width="32"
          height="32"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <path d="M12 2L2 7l10 5 10-5-10-5z" />
          <path d="m2 17 10 5 10-5" />
          <path d="m2 12 10 5 10-5" />
        </svg>
      ),
      title: "Unified Analytics Dashboard",
      description:
        "Centralized platform that aggregates data from all major social media and streaming platforms in real-time.",
      color: "primary",
    },
    {
      icon: (
        <svg
          width="32"
          height="32"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" />
          <circle cx="12" cy="12" r="4" />
        </svg>
      ),
      title: "AI-Powered Viral Prediction",
      description:
        "Machine learning algorithms that analyze engagement patterns, demographics, and trends to predict viral potential.",
      color: "accent",
    },
    {
      icon: (
        <svg
          width="32"
          height="32"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" />
        </svg>
      ),
      title: "Smart Recommendations Engine",
      description:
        "Data-driven insights that provide specific, actionable recommendations to improve song performance and reach.",
      color: "success",
    },
  ];

  return (
    <div
      className="viral-hits-isolated"
      style={{
        all: "unset",
        display: "block",
        fontFamily: '"Inter", sans-serif !important',
        fontSize: "14px !important",
        color: "#1a1a1a !important",
        backgroundColor: "#fafafa !important",
        lineHeight: "1.6 !important",
        fontWeight: "400 !important",
        boxSizing: "border-box",
      }}
    >
      <section className="py-24 px-6 bg-gradient-to-br from-background to-accent/5 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-accent/5"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-accent/10 rounded-full blur-3xl"></div>

        <div className="max-w-7xl mx-auto relative z-10">
          {/* Section Header */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-3 px-8 py-4 rounded-full mb-8 modern-card border border-primary/30 shadow-lg">
              <span className="text-lg font-medium text-foreground font-inter">
                Problem & Solution
              </span>
            </div>

            <h2 className="text-6xl font-bold mb-8 font-inter text-foreground">
              Music Analytics Challenges
            </h2>

            <p className="text-xl max-w-4xl mx-auto leading-relaxed font-inter text-muted">
              The music industry faces significant challenges in tracking and
              predicting song performance across multiple digital platforms. Our
              solution addresses these pain points with intelligent analytics.
            </p>
          </div>

          {/* Challenges Grid */}
          <div className="mb-20">
            <h3 className="text-4xl font-bold mb-12 font-inter text-foreground text-center">
              Industry Challenges
            </h3>

            <div className="grid md:grid-cols-2 gap-8">
              {challenges.map((challenge, index) => (
                <div
                  key={index}
                  className="modern-card border border-destructive/20 rounded-3xl p-8 modern-card-hover animate-fade-in-up"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="w-16 h-16 rounded-2xl bg-destructive/10 flex items-center justify-center text-destructive shrink-0">
                      {challenge.icon}
                    </div>

                    <div>
                      <h4 className="text-2xl font-bold font-inter text-foreground mb-4">
                        {challenge.title}
                      </h4>
                      <p className="font-inter text-muted leading-relaxed">
                        {challenge.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Arrow Transition */}
          <div className="flex justify-center mb-20 animate-bounce-gentle">
            <div className="w-16 h-16 rounded-full bg-primary flex items-center justify-center shadow-lg">
              <svg
                width="32"
                height="32"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                className="text-white"
              >
                <path d="M12 5v14M5 12l7 7 7-7" />
              </svg>
            </div>
          </div>

          {/* Solutions Grid */}
          <div>
            <h3 className="text-4xl font-bold mb-12 font-inter text-foreground text-center">
              Our Solutions
            </h3>

            <div className="grid md:grid-cols-3 gap-8">
              {solutions.map((solution, index) => (
                <div
                  key={index}
                  className="modern-card border border-primary/20 rounded-3xl p-8 text-center modern-card-hover animate-fade-in-up"
                  style={{ animationDelay: `${index * 0.1 + 0.5}s` }}
                >
                  <div
                    className={`w-20 h-20 mx-auto mb-6 rounded-2xl bg-${solution.color} flex items-center justify-center text-white shadow-lg`}
                  >
                    {solution.icon}
                  </div>

                  <h4 className="text-2xl font-bold font-inter text-foreground mb-4">
                    {solution.title}
                  </h4>
                  <p className="font-inter text-muted leading-relaxed">
                    {solution.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
