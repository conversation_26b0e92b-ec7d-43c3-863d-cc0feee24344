export function InvolvementSection() {
  const workSegments = [
    {
      title: "Wireframing",
      icon: (
        <svg
          width="32"
          height="32"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <rect x="3" y="3" width="18" height="18" rx="2" />
          <path d="M9 9h6v6H9z" />
        </svg>
      ),
      color: "bg-[#3366FF]", // Lighter blue
      position: { x: 15, y: 75 },
    },
    {
      title: "UI/UX Design",
      icon: (
        <svg
          width="32"
          height="32"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <path d="M12 2v20M2 12h20" />
          <circle cx="12" cy="12" r="3" />
        </svg>
      ),
      color: "bg-[#1A4FE6]", // Medium light blue
      position: { x: 8, y: 35 },
    },
    {
      title: "Full-Stack Development",
      icon: (
        <svg
          width="32"
          height="32"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <rect x="2" y="3" width="20" height="14" rx="2" />
          <path d="M8 21h8M12 17v4" />
          <path d="M6 8h.01M10 8h4" />
        </svg>
      ),
      color: "bg-[#0052FD]", // Primary blue
      position: { x: 50, y: 10 },
    },
    {
      title: "Analytics Integration",
      icon: (
        <svg
          width="32"
          height="32"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <path d="M3 3v18h18" />
          <path d="M18 17V9" />
          <path d="M13 17V5" />
          <path d="M8 17v-3" />
        </svg>
      ),
      color: "bg-[#0041CC]", // Medium dark blue
      position: { x: 92, y: 35 },
    },
    {
      title: "Support & Maintenance",
      icon: (
        <svg
          width="32"
          height="32"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" />
          <path d="M8 12l2 2 4-4" />
        </svg>
      ),
      color: "bg-[#003399]", // Darker blue
      position: { x: 85, y: 75 },
    },
  ];

  return (
    <section className="py-24 px-6 bg-gradient-to-br from-slate-100 via-slate-50 to-blue-50 relative">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-2 bg-[#0052FD] px-6 py-3 rounded-full mb-8 shadow-lg">
            <span className="text-base text-white font-medium font-['DM_Sans',_sans-serif]">
              Development Process
            </span>
          </div>

          <h2 className="text-5xl font-bold mb-8 text-[#1E1E1E] font-['DM_Sans',_sans-serif]">
            Our Development Journey
          </h2>

          <p className="text-xl text-[#1E1E1E] opacity-70 max-w-4xl mx-auto leading-relaxed font-['DM_Sans',_sans-serif]">
            From initial concept to final deployment, we follow a comprehensive
            development process that ensures quality, efficiency, and successful
            project delivery.
          </p>
        </div>

        {/* Semi-Circular Layout with Doodle Arrows */}
        <div className="relative flex justify-center">
          <div className="relative w-[800px] h-[500px]">
            {/* Doodle-Style Curved Flow Arrows */}
            <svg
              className="absolute inset-0 w-full h-full pointer-events-none"
              viewBox="0 0 100 100"
              preserveAspectRatio="none"
            >
              {/* Doodle Arrow from Wireframing to UI/UX */}
              <path
                d="M 18 78 Q 14 70 12 60 Q 11 55 11.5 50 Q 12 48 12.5 47"
                stroke="#0052FD"
                strokeWidth="0.4"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                markerEnd="url(#doodleArrowhead)"
              />
              {/* Doodle Arrow from UI/UX to Full-Stack */}
              <path
                d="M 15 45 Q 18 42 22 38 Q 28 32 35 28 Q 40 25 45 20 Q 47 18 48 16"
                stroke="#0052FD"
                strokeWidth="0.4"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                markerEnd="url(#doodleArrowhead)"
              />
              {/* Doodle Arrow from Full-Stack to Analytics */}
              <path
                d="M 52 16 Q 55 18 60 22 Q 65 26 72 32 Q 78 38 82 42 Q 84 44 85 45"
                stroke="#0052FD"
                strokeWidth="0.4"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                markerEnd="url(#doodleArrowhead)"
              />
              {/* Doodle Arrow from Analytics to Support */}
              <path
                d="M 88 50 Q 88.5 52 89 55 Q 89 60 88 65 Q 87 70 85 75 Q 84 77 83 78"
                stroke="#0052FD"
                strokeWidth="0.4"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                markerEnd="url(#doodleArrowhead)"
              />

              <defs>
                <marker
                  id="doodleArrowhead"
                  markerWidth="4"
                  markerHeight="3.5"
                  refX="3.5"
                  refY="1.75"
                  orient="auto"
                >
                  <path
                    d="M 0,0 L 0,3.5 L 4,1.75 z"
                    fill="#0052FD"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </marker>
              </defs>
            </svg>

            {/* Work Segments */}
            {workSegments.map((segment, index) => (
              <div
                key={index}
                className="absolute transform -translate-x-1/2 -translate-y-1/2"
                style={{
                  left: `${segment.position.x}%`,
                  top: `${segment.position.y}%`,
                }}
              >
                {/* Segment Shape */}
                <div
                  className={`${segment.color} rounded-3xl px-8 py-6 shadow-lg flex flex-col items-center justify-center min-w-[180px] min-h-[120px] relative hover:scale-105 transition-transform duration-300`}
                >
                  {/* Icon */}
                  <div className="text-white mb-3">{segment.icon}</div>

                  {/* Title */}
                  <h3 className="text-white font-bold text-center leading-tight font-['DM_Sans',_sans-serif]">
                    {segment.title}
                  </h3>
                </div>
              </div>
            ))}

            {/* Central "Scope of Work" Circle */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <div className="w-48 h-48 bg-white rounded-full shadow-2xl flex flex-col items-center justify-center border-4 border-[#0052FD]">
                <h3 className="text-3xl font-bold text-[#0052FD] text-center leading-tight font-['DM_Sans',_sans-serif]">
                  Scope
                  <br />
                  of Work
                </h3>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Content */}
        <div className="mt-24 text-center">
          <div className="bg-white rounded-3xl p-12 shadow-lg border border-blue-200 max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-[#1E1E1E] mb-6 font-['DM_Sans',_sans-serif]">
              Complete Development Cycle
            </h3>
            <p className="text-lg text-[#1E1E1E] opacity-70 leading-relaxed font-['DM_Sans',_sans-serif] mb-8">
              Our structured approach ensures every aspect of your project is
              carefully planned, designed, developed, and maintained to deliver
              exceptional results that exceed expectations.
            </p>

            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-[#0052FD]/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">⚡</span>
                </div>
                <h4 className="font-bold text-[#1E1E1E] mb-2 font-['DM_Sans',_sans-serif]">
                  Fast Delivery
                </h4>
                <p className="text-sm text-[#1E1E1E] opacity-70 font-['DM_Sans',_sans-serif]">
                  Efficient processes ensure timely project completion
                </p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-[#0052FD]/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🎯</span>
                </div>
                <h4 className="font-bold text-[#1E1E1E] mb-2 font-['DM_Sans',_sans-serif]">
                  Quality Focus
                </h4>
                <p className="text-sm text-[#1E1E1E] opacity-70 font-['DM_Sans',_sans-serif]">
                  Rigorous testing and quality assurance standards
                </p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-[#0052FD]/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🤝</span>
                </div>
                <h4 className="font-bold text-[#1E1E1E] mb-2 font-['DM_Sans',_sans-serif]">
                  Ongoing Support
                </h4>
                <p className="text-sm text-[#1E1E1E] opacity-70 font-['DM_Sans',_sans-serif]">
                  Continuous maintenance and feature updates
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
