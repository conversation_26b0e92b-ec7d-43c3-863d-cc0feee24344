// import laptopMockup from 'figma:asset/df9bea4e247b19b1bb7de0a8a26629c5a8fbfeb7.png';
// import analyticsImage from 'figma:asset/71b09aa11a0fc155dd05816baeef268baca3428b.png';
// import ehrImage from 'figma:asset/ec8ea2eea5483eee04334abd7b2450e39f58d754.png';

export function AboutSection() {
  const keyFeatures = [
    {
      icon: "👩‍⚕️",
      title: "Provider Dashboard",
      description:
        "Comprehensive interface for healthcare providers to manage patients, appointments, and medical records with real-time updates and intuitive navigation. Features customizable widgets, priority alerts for urgent cases, and seamless integration with existing hospital management systems to streamline daily workflows and enhance patient care delivery.",
    },
    {
      icon: "📱",
      title: "Patient Portal",
      description:
        "User-friendly mobile and web application enabling patients to book appointments, view medical records, and communicate directly with their care team. Includes automated medication reminders, appointment notifications, prescription refill requests, and educational resources tailored to each patient's specific conditions and treatment plans.",
    },
    {
      icon: "💬",
      title: "Telemedicine Integration",
      description:
        "Secure video consultation platform with integrated prescription management, diagnosis tools, and seamless patient-provider communication. Supports HD video consultations, screen sharing capabilities, digital prescription management with e-prescribing, and automated insurance verification for comprehensive remote healthcare delivery.",
    },
    {
      icon: "📊",
      title: "Health Analytics & Reporting",
      description:
        "Advanced analytics engine providing insights into population health management, treatment optimization, and clinical decision support. Includes predictive modeling, risk stratification algorithms, early warning systems for potential health issues, and interactive dashboards that present complex medical data in easily digestible formats.",
    },
    {
      icon: "🔒",
      title: "Security & Compliance",
      description:
        "Enterprise-grade security architecture with HIPAA compliance, end-to-end encryption, comprehensive audit trails, and advanced data protection protocols. Features 256-bit encryption, multi-factor authentication with biometric options, zero-trust architecture, and continuous monitoring with AI-powered threat detection systems.",
    },
    {
      icon: "🔄",
      title: "EHR System Integration",
      description:
        "Seamless integration with existing Electronic Health Record systems, medical devices, and third-party healthcare applications for unified workflows. Supports real-time bidirectional data synchronization, automated data validation, custom integration workflows, and direct data import from patient monitoring equipment and laboratory systems.",
    },
  ];

  return (
    <section className="py-24 px-6 bg-white">
      <div className="max-w-7xl mx-auto">
        {/* Project Overview */}
        <div className="mb-32">
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 bg-[#0052FD] px-6 py-3 rounded-full mb-8 shadow-lg">
              <span className="text-base text-white font-medium font-['DM_Sans',_sans-serif]">
                Project Overview
              </span>
            </div>

            <h2 className="text-5xl font-bold mb-8 leading-tight text-[#1E1E1E] font-['DM_Sans',_sans-serif]">
              Transforming Healthcare
              <span className="block text-[#0052FD]">Through Innovation</span>
            </h2>

            <p className="text-xl max-w-4xl mx-auto leading-relaxed text-[#1E1E1E] opacity-70 mb-16 font-['DM_Sans',_sans-serif]">
              DocMobil revolutionizes healthcare delivery by creating an
              integrated ecosystem that enhances patient outcomes, streamlines
              provider workflows, and ensures the highest standards of care
              through cutting-edge technology.
            </p>
          </div>

          {/* Key Features - First 2 features */}
          <div className="space-y-16">
            {keyFeatures.slice(0, 2).map((feature, index) => (
              <div key={index} className="flex items-start gap-8 group">
                <div className="flex-shrink-0">
                  <div className="w-20 h-20 bg-gradient-to-br from-[#E6EEFF] to-[#0052FD]/20 rounded-3xl flex items-center justify-center group-hover:from-[#0052FD]/10 group-hover:to-[#0052FD]/30 transition-all duration-300 shadow-sm border border-[#E7E8E9]">
                    <span className="text-3xl">{feature.icon}</span>
                  </div>
                </div>

                <div className="flex-1 min-w-0">
                  <h3 className="text-2xl font-bold text-[#1E1E1E] mb-4 font-['DM_Sans',_sans-serif]">
                    {feature.title}
                  </h3>

                  <p className="text-lg text-[#1E1E1E] opacity-70 leading-relaxed max-w-4xl font-['DM_Sans',_sans-serif]">
                    {feature.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* First Laptop Mockup - Patient Portal */}
          <div className="my-20 flex justify-center">
            <img
              src={"laptopMockup"}
              alt="DocMobil Appointments Dashboard"
              className="w-full max-w-5xl h-auto object-contain"
            />
          </div>

          {/* Features 3-4 (Telemedicine Integration, Health Analytics & Reporting) */}
          <div className="space-y-16">
            {keyFeatures.slice(2, 4).map((feature, index) => (
              <div key={index + 2} className="flex items-start gap-8 group">
                <div className="flex-shrink-0">
                  <div className="w-20 h-20 bg-gradient-to-br from-[#E6EEFF] to-[#0052FD]/20 rounded-3xl flex items-center justify-center group-hover:from-[#0052FD]/10 group-hover:to-[#0052FD]/30 transition-all duration-300 shadow-sm border border-[#E7E8E9]">
                    <span className="text-3xl">{feature.icon}</span>
                  </div>
                </div>

                <div className="flex-1 min-w-0">
                  <h3 className="text-2xl font-bold text-[#1E1E1E] mb-4 font-['DM_Sans',_sans-serif]">
                    {feature.title}
                  </h3>

                  <p className="text-lg text-[#1E1E1E] opacity-70 leading-relaxed max-w-4xl font-['DM_Sans',_sans-serif]">
                    {feature.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Second Mockup - After Health Analytics & Reporting */}
          <div className="my-20 flex justify-center">
            <img
              src={"analyticsImage"}
              alt="DocMobil Analytics Dashboard"
              className="w-full max-w-5xl h-auto object-contain"
            />
          </div>

          {/* Remaining Features (Security & Compliance, EHR System Integration) */}
          <div className="space-y-16">
            {keyFeatures.slice(4).map((feature, index) => (
              <div key={index + 4} className="flex items-start gap-8 group">
                <div className="flex-shrink-0">
                  <div className="w-20 h-20 bg-gradient-to-br from-[#E6EEFF] to-[#0052FD]/20 rounded-3xl flex items-center justify-center group-hover:from-[#0052FD]/10 group-hover:to-[#0052FD]/30 transition-all duration-300 shadow-sm border border-[#E7E8E9]">
                    <span className="text-3xl">{feature.icon}</span>
                  </div>
                </div>

                <div className="flex-1 min-w-0">
                  <h3 className="text-2xl font-bold text-[#1E1E1E] mb-4 font-['DM_Sans',_sans-serif]">
                    {feature.title}
                  </h3>

                  <p className="text-lg text-[#1E1E1E] opacity-70 leading-relaxed max-w-4xl font-['DM_Sans',_sans-serif]">
                    {feature.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Third Mockup - After EHR System Integration */}
          <div className="my-20 flex justify-center">
            <img
              src={"ehrImage"}
              alt="DocMobil EHR Integration"
              className="w-full max-w-5xl h-auto object-contain"
            />
          </div>
        </div>

        {/* Project Impact */}
        <div className="bg-gradient-to-r from-[#0052FD] to-[#0052FD]/90 text-white rounded-3xl p-12">
          <h3 className="text-3xl font-bold text-center mb-4 font-['DM_Sans',_sans-serif]">
            Transforming Healthcare Delivery
          </h3>
          <p className="text-center text-[#E6EEFF] text-lg mb-12 max-w-3xl mx-auto font-['DM_Sans',_sans-serif]">
            Our platform has revolutionized how healthcare providers deliver
            care, resulting in improved patient outcomes and operational
            efficiency across the board.
          </p>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-5xl font-bold mb-3 text-[#E6EEFF] font-['DM_Sans',_sans-serif]">
                40%
              </div>
              <div className="text-lg font-medium mb-2 font-['DM_Sans',_sans-serif]">
                Faster Processing
              </div>
              <div className="text-sm opacity-80 font-['DM_Sans',_sans-serif]">
                Patient workflow improvement
              </div>
            </div>
            <div className="text-center">
              <div className="text-5xl font-bold mb-3 text-[#E6EEFF] font-['DM_Sans',_sans-serif]">
                95%
              </div>
              <div className="text-lg font-medium mb-2 font-['DM_Sans',_sans-serif]">
                User Satisfaction
              </div>
              <div className="text-sm opacity-80 font-['DM_Sans',_sans-serif]">
                Healthcare provider rating
              </div>
            </div>
            <div className="text-center">
              <div className="text-5xl font-bold mb-3 text-[#E6EEFF] font-['DM_Sans',_sans-serif]">
                60%
              </div>
              <div className="text-lg font-medium mb-2 font-['DM_Sans',_sans-serif]">
                Time Saved
              </div>
              <div className="text-sm opacity-80 font-['DM_Sans',_sans-serif]">
                On administrative tasks
              </div>
            </div>
            <div className="text-center">
              <div className="text-5xl font-bold mb-3 text-[#E6EEFF] font-['DM_Sans',_sans-serif]">
                24/7
              </div>
              <div className="text-lg font-medium mb-2 font-['DM_Sans',_sans-serif]">
                Availability
              </div>
              <div className="text-sm opacity-80 font-['DM_Sans',_sans-serif]">
                Patient access to care
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
