export function ChallengesSection() {
  return (
    <section className="py-24 px-6 bg-[#1E1E1E]">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-[#0052FD] px-6 py-3 rounded-full mb-8 shadow-lg">
            <span className="text-base text-white font-medium font-['DM_Sans',_sans-serif]">
              Problem & Solution
            </span>
          </div>

          <h2 className="text-5xl font-bold mb-8 text-white font-['DM_Sans',_sans-serif]">
            Addressing Healthcare
            <span className="block text-[#0052FD]">Challenges</span>
          </h2>

          <p className="text-xl text-[#E7E8E9] leading-relaxed font-['DM_Sans',_sans-serif] max-w-4xl mx-auto">
            DocMobil tackles the most pressing issues in modern healthcare
            delivery while providing innovative solutions that enhance patient
            care and provider efficiency.
          </p>
        </div>

        {/* Problem & Solution Grid */}
        <div className="grid lg:grid-cols-2 gap-12">
          {/* Healthcare Challenges */}
          <div className="bg-gradient-to-br from-red-50 to-orange-50 rounded-3xl p-10 border border-red-100">
            <div className="flex items-center gap-4 mb-8">
              <div className="w-16 h-16 bg-red-100 rounded-2xl flex items-center justify-center">
                <span className="text-red-600 text-2xl">⚠️</span>
              </div>
              <h3 className="text-3xl font-bold text-[#1E1E1E] font-['DM_Sans',_sans-serif]">
                Healthcare Challenges
              </h3>
            </div>

            <div className="space-y-6">
              <div className="flex items-start gap-4">
                <div className="w-3 h-3 bg-red-500 rounded-full mt-3 flex-shrink-0"></div>
                <p className="text-[#1E1E1E] leading-relaxed font-['DM_Sans',_sans-serif] text-lg">
                  Fragmented patient data across multiple systems leading to
                  incomplete care histories
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="w-3 h-3 bg-red-500 rounded-full mt-3 flex-shrink-0"></div>
                <p className="text-[#1E1E1E] leading-relaxed font-['DM_Sans',_sans-serif] text-lg">
                  Time-consuming manual processes reducing face-to-face patient
                  interaction time
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="w-3 h-3 bg-red-500 rounded-full mt-3 flex-shrink-0"></div>
                <p className="text-[#1E1E1E] leading-relaxed font-['DM_Sans',_sans-serif] text-lg">
                  Limited accessibility for patients in remote areas or with
                  mobility constraints
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="w-3 h-3 bg-red-500 rounded-full mt-3 flex-shrink-0"></div>
                <p className="text-[#1E1E1E] leading-relaxed font-['DM_Sans',_sans-serif] text-lg">
                  Complex scheduling systems causing appointment conflicts and
                  patient dissatisfaction
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="w-3 h-3 bg-red-500 rounded-full mt-3 flex-shrink-0"></div>
                <p className="text-[#1E1E1E] leading-relaxed font-['DM_Sans',_sans-serif] text-lg">
                  Compliance and security concerns with sensitive medical data
                  management
                </p>
              </div>
            </div>
          </div>

          {/* Healthcare Solution */}
          <div className="bg-gradient-to-br from-green-50 to-blue-50 rounded-3xl p-10 border border-green-100">
            <div className="flex items-center gap-4 mb-8">
              <div className="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center">
                <span className="text-green-600 text-2xl">✅</span>
              </div>
              <h3 className="text-3xl font-bold text-[#1E1E1E] font-['DM_Sans',_sans-serif]">
                Our Healthcare Solution
              </h3>
            </div>

            <div className="space-y-6">
              <div className="flex items-start gap-4">
                <div className="w-3 h-3 bg-green-500 rounded-full mt-3 flex-shrink-0"></div>
                <p className="text-[#1E1E1E] leading-relaxed font-['DM_Sans',_sans-serif] text-lg">
                  Unified patient records with real-time synchronization across
                  all healthcare providers
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="w-3 h-3 bg-green-500 rounded-full mt-3 flex-shrink-0"></div>
                <p className="text-[#1E1E1E] leading-relaxed font-['DM_Sans',_sans-serif] text-lg">
                  Automated workflows and AI-assisted documentation to maximize
                  patient care time
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="w-3 h-3 bg-green-500 rounded-full mt-3 flex-shrink-0"></div>
                <p className="text-[#1E1E1E] leading-relaxed font-['DM_Sans',_sans-serif] text-lg">
                  Comprehensive telemedicine platform enabling remote
                  consultations and monitoring
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="w-3 h-3 bg-green-500 rounded-full mt-3 flex-shrink-0"></div>
                <p className="text-[#1E1E1E] leading-relaxed font-['DM_Sans',_sans-serif] text-lg">
                  Intelligent scheduling system with conflict resolution and
                  patient preference matching
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="w-3 h-3 bg-green-500 rounded-full mt-3 flex-shrink-0"></div>
                <p className="text-[#1E1E1E] leading-relaxed font-['DM_Sans',_sans-serif] text-lg">
                  Bank-level security with HIPAA compliance and comprehensive
                  audit trails
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
