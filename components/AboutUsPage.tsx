import { useEffect, useState } from "react";
import {
  ArrowRight,
  Target,
  Eye,
  Linkedin,
  Twitter,
  Github,
  Star,
  Quote,
  Brain,
  TrendingUp,
  Sparkles,
  Code,
  Users,
  Shield,
  Clock,
} from "lucide-react";

const AboutUsPage = () => {
  const [isVisible, setIsVisible] = useState<Record<number, boolean>>({});

  useEffect(() => {
    const handleScroll = () => {
      const elements = document.querySelectorAll(".scroll-reveal");
      const newVisible: Record<number, boolean> = {};

      elements.forEach((element, index) => {
        const rect = element.getBoundingClientRect();
        if (rect.top < window.innerHeight - 50) {
          newVisible[index] = true;
        } else {
          newVisible[index] = isVisible[index] || false;
        }
      });

      setIsVisible(newVisible);
    };

    // Initial check to show content immediately
    setTimeout(handleScroll, 100);

    window.addEventListener("scroll", handleScroll);
    window.addEventListener("resize", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
      window.removeEventListener("resize", handleScroll);
    };
  }, []);

  // Force initial visibility after component mount
  useEffect(() => {
    const timer = setTimeout(() => {
      const elements = document.querySelectorAll(".scroll-reveal");
      const initialVisible: Record<number, boolean> = {};
      elements.forEach((_, index) => {
        initialVisible[index] = true;
      });
      setIsVisible(initialVisible);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const scrollToContact = () => {
    const contactSection = document.getElementById("contact");
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: "smooth" });
    }
  };

  const founders = [
    {
      name: "Junaid Khan",
      title: "CEO & Founder",
      image:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      social: {
        linkedin: "https://linkedin.com/in/junaidkhan",
        twitter: "https://twitter.com/junaidkhan",
        github: "https://github.com/junaidkhan",
      },
    },
    {
      name: "Daniyal Khan",
      title: "COO & Founder",
      image:
        "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      social: {
        linkedin: "https://linkedin.com/in/daniyalkhan",
        twitter: "https://twitter.com/daniyalkhan",
        github: "https://github.com/daniyalkhan",
      },
    },
  ];

  const clientLogos = [
    {
      name: "Microsoft",
      logo: "https://images.unsplash.com/photo-1633409361618-c73427e4e206?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80",
    },
    {
      name: "Amazon",
      logo: "https://images.unsplash.com/photo-1523474253046-8cd2748b5fd2?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80",
    },
    {
      name: "Tesla",
      logo: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80",
    },
    {
      name: "Google",
      logo: "https://images.unsplash.com/photo-1573804633927-bfcbcd909acd?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80",
    },
    {
      name: "Apple",
      logo: "https://images.unsplash.com/photo-1611532736597-de2d4265fba3?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80",
    },
    {
      name: "Netflix",
      logo: "https://images.unsplash.com/photo-1574375927938-d5a98e8ffe85?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80",
    },
    {
      name: "Spotify",
      logo: "https://images.unsplash.com/photo-1611339555312-e607c8352fd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80",
    },
    {
      name: "Uber",
      logo: "https://images.unsplash.com/photo-1599305445671-ac291c95aaa9?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80",
    },
  ];

  const testimonials = [
    {
      quote:
        "Black Lion Software transformed our entire business infrastructure with their AI-powered solutions. Their team delivered beyond expectations, resulting in 40% efficiency improvement and significant cost savings.",
      author: "Sarah Mitchell",
      title: "Chief Technology Officer",
      company: "TechFlow Industries",
      avatar:
        "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80",
      logo: "https://images.unsplash.com/photo-1633409361618-c73427e4e206?ixlib=rb-4.0.3&auto=format&fit=crop&w=60&q=80",
      rating: 5,
    },
    {
      quote:
        "Working with Black Lion was a game-changer for our digital transformation. Their blockchain expertise helped us revolutionize our supply chain, increasing transparency by 95% and building unprecedented customer trust.",
      author: "Michael Chen",
      title: "Head of Innovation",
      company: "Global Logistics Corp",
      avatar:
        "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80",
      logo: "https://images.unsplash.com/photo-1523474253046-8cd2748b5fd2?ixlib=rb-4.0.3&auto=format&fit=crop&w=60&q=80",
      rating: 5,
    },
    {
      quote:
        "Their AI-driven analytics platform gave us insights we never knew were possible. The real-time data processing has transformed how we make business decisions, leading to 25% revenue growth in just 6 months.",
      author: "Dr. Amanda Foster",
      title: "VP of Data Science",
      company: "FinTech Solutions",
      avatar:
        "https://images.unsplash.com/photo-1494790108755-2616b612c88e?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80",
      logo: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=60&q=80",
      rating: 5,
    },
  ];

  return (
    <div className="min-h-screen bg-black text-white overflow-x-hidden">
      {/* 1. Hero Section */}
      <section
        className="relative min-h-[90vh] flex items-center justify-center overflow-hidden"
        style={{
          background:
            "radial-gradient(ellipse at center, #1a1a1a 0%, #0a0a0a 70%)",
        }}
      >
        {/* Background Image */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-50"
          style={{
            backgroundImage:
              "url(https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2670&q=80)",
          }}
        ></div>

        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/75 via-black/55 to-black/75"></div>

        {/* Tech-inspired animation background */}
        <div className="particles">
          {[...Array(40)].map((_, i) => (
            <div
              key={i}
              className="particle"
              style={{
                left: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 15}s`,
                animationDuration: `${15 + Math.random() * 10}s`,
              }}
            />
          ))}
        </div>

        {/* Animated tech grid overlay */}
        <div className="absolute inset-0 opacity-10">
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `
                linear-gradient(rgba(84, 37, 176, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(84, 37, 176, 0.1) 1px, transparent 1px)
              `,
              backgroundSize: "50px 50px",
              animation: "pulse 4s ease-in-out infinite",
            }}
          />
        </div>

        {/* Content */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="animate-fade-in-up">
            {/* Main Heading */}
            <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-display font-black mb-8 leading-tight">
              From Idea to Impact:
              <span className="block gradient-text-primary mt-4">
                Scalable Web, App, Blockchain &amp; AI Solutions
              </span>
              <span className="block text-3xl sm:text-4xl lg:text-5xl xl:text-6xl mt-6 text-gray-300">
                That Accelerate Your Success
              </span>
            </h1>

            {/* Subheading */}
            <p className="text-xl sm:text-2xl lg:text-3xl font-display font-semibold text-gray-300 mb-12 max-w-3xl mx-auto">
              Bold innovation. Lasting transformation.
            </p>

            {/* CTA Button */}
            <button
              onClick={scrollToContact}
              className="btn-modern text-xl px-10 py-5 group"
            >
              Let's Connect
              <ArrowRight className="ml-3 w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" />
            </button>
          </div>
        </div>

        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* 2. Our Origin Story */}
      <section
        className="py-20 lg:py-32"
        style={{
          background: "linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%)",
        }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left-aligned paragraph */}
            <div className="opacity-100 transform-none">
              <h2
                className="text-4xl sm:text-5xl lg:text-6xl font-display font-bold mb-8 text-white"
                style={{
                  background:
                    "linear-gradient(135deg, #5425B0 0%, #8b5cf6 50%, #a855f7 100%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  backgroundClip: "text",
                }}
              >
                Our Origin Story
              </h2>
              <div className="space-y-6 text-lg leading-relaxed text-gray-200">
                <p className="text-white/90">
                  Before ChatGPT even existed, we were already building
                  next-generation AI for entertainment—crafting dynamic story
                  engines and delivering real-time audience insights. When our
                  proprietary platform proved its worth, we expanded into every
                  industry, offering full-stack web, mobile, blockchain and AI
                  solutions for travel, finance, healthcare and beyond.
                </p>
                <p className="text-white/90">
                  Today, our battle-tested infrastructure empowers innovators
                  across all sectors—because no challenge is too great when you
                  pair vision with smart software.
                </p>
              </div>

              {/* Key achievements */}
              <div className="grid grid-cols-2 gap-6 mt-8">
                <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-6 hover:bg-white/15 transition-all duration-300">
                  <div className="flex items-center gap-3 mb-3">
                    <Clock className="w-6 h-6 text-purple-400" />
                    <span className="text-2xl font-display font-bold text-purple-400">
                      2018+
                    </span>
                  </div>
                  <p className="text-sm text-white/80 font-medium">
                    Building AI before it was mainstream
                  </p>
                </div>
                <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-6 hover:bg-white/15 transition-all duration-300">
                  <div className="flex items-center gap-3 mb-3">
                    <Users className="w-6 h-6 text-purple-400" />
                    <span className="text-2xl font-display font-bold text-purple-400">
                      500+
                    </span>
                  </div>
                  <p className="text-sm text-white/80 font-medium">
                    Projects delivered worldwide
                  </p>
                </div>
              </div>
            </div>

            {/* Right-side visual */}
            <div className="lg:order-2">
              <div className="relative">
                {/* Main visual container */}
                <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-3xl p-8">
                  <div className="grid grid-cols-2 gap-6">
                    {/* Tech icons */}
                    <div className="flex flex-col items-center p-6 bg-purple-500/10 rounded-2xl">
                      <Brain className="w-16 h-16 text-purple-400 mb-4" />
                      <span className="text-sm font-display font-semibold text-white">
                        AI Innovation
                      </span>
                    </div>
                    <div className="flex flex-col items-center p-6 bg-purple-500/10 rounded-2xl">
                      <Code className="w-16 h-16 text-purple-400 mb-4" />
                      <span className="text-sm font-display font-semibold text-white">
                        Full-Stack Development
                      </span>
                    </div>
                    <div className="flex flex-col items-center p-6 bg-purple-500/10 rounded-2xl">
                      <Shield className="w-16 h-16 text-purple-400 mb-4" />
                      <span className="text-sm font-display font-semibold text-white">
                        Blockchain Security
                      </span>
                    </div>
                    <div className="flex flex-col items-center p-6 bg-purple-500/10 rounded-2xl">
                      <TrendingUp className="w-16 h-16 text-purple-400 mb-4" />
                      <span className="text-sm font-display font-semibold text-white">
                        Business Growth
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 3. Mission & Vision Section */}
      <section className="py-20 lg:py-32 bg-black">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Two-column layout */}
          <div className="grid lg:grid-cols-2 gap-12 lg:gap-16">
            {/* Left: "Our Mission" */}
            <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-3xl p-8">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-14 h-14 bg-gradient-to-r from-purple-600 to-purple-500 rounded-xl flex items-center justify-center">
                  <Target className="w-7 h-7 text-white" />
                </div>
                <h2 className="text-3xl lg:text-4xl font-display font-bold gradient-text-primary">
                  Our Mission
                </h2>
              </div>
              <div className="w-full h-1 bg-gradient-to-r from-purple-600 to-purple-500 opacity-30 rounded-full mb-6"></div>
              <p className="text-lg text-gray-300 leading-relaxed">
                To partner with organizations in architecting and delivering
                tailored AI, web, mobile, and blockchain solutions —
                accelerating innovation, driving sustainable growth, and turning
                bold ideas into lasting impact.
              </p>
            </div>

            {/* Right: "Our Vision" */}
            <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-3xl p-8">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-14 h-14 bg-gradient-to-r from-purple-600 to-purple-500 rounded-xl flex items-center justify-center">
                  <Eye className="w-7 h-7 text-white" />
                </div>
                <h2 className="text-3xl lg:text-4xl font-display font-bold gradient-text-primary">
                  Our Vision
                </h2>
              </div>
              <div className="w-full h-1 bg-gradient-to-r from-purple-600 to-purple-500 opacity-30 rounded-full mb-6"></div>
              <p className="text-lg text-gray-300 leading-relaxed">
                To lead AI digital transformation — empowering businesses with
                intelligent software that drives innovation, efficiency, and
                lasting impact across industries.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 4. Meet the Founders */}
      <section
        className="py-20 lg:py-32"
        style={{
          background: "linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%)",
        }}
      >
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl lg:text-6xl font-display font-bold mb-6 gradient-text-primary">
              Meet the Founders
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Visionary leaders driving the future of AI and digital
              transformation
            </p>
          </div>

          {/* Card-style profile blocks */}
          <div className="grid md:grid-cols-2 gap-12 lg:gap-16">
            {founders.map((founder, index) => (
              <div
                key={index}
                className="bg-white/5 backdrop-blur-md border border-white/10 rounded-3xl p-8 text-center group hover:transform hover:scale-105 transition-all duration-300"
              >
                <div className="relative inline-block mb-8">
                  {/* Profile photos */}
                  <div className="relative">
                    <div className="w-40 h-40 mx-auto relative">
                      <div className="w-full h-full rounded-full overflow-hidden border-4 border-purple-500/30 group-hover:border-purple-500 transition-all duration-300">
                        <img
                          src={founder.image}
                          alt={founder.name}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Name, title */}
                <h3 className="text-3xl lg:text-4xl font-display font-black mb-3 text-white">
                  {founder.name}
                </h3>
                <p className="text-xl text-purple-400 font-semibold mb-8 font-display">
                  {founder.title}
                </p>

                {/* Social media icons */}
                <div className="flex justify-center gap-4">
                  <a
                    href={founder.social.linkedin}
                    className="w-14 h-14 bg-white/10 rounded-full flex items-center justify-center hover:bg-purple-600 hover:scale-110 transition-all duration-300"
                  >
                    <Linkedin className="w-6 h-6 text-white" />
                  </a>
                  <a
                    href={founder.social.twitter}
                    className="w-14 h-14 bg-white/10 rounded-full flex items-center justify-center hover:bg-purple-600 hover:scale-110 transition-all duration-300"
                  >
                    <Twitter className="w-6 h-6 text-white" />
                  </a>
                  <a
                    href={founder.social.github}
                    className="w-14 h-14 bg-white/10 rounded-full flex items-center justify-center hover:bg-purple-600 hover:scale-110 transition-all duration-300"
                  >
                    <Github className="w-6 h-6 text-white" />
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* 5. Client Logos */}
      <section className="py-20 lg:py-32 bg-black relative overflow-hidden">
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-display font-bold mb-6 gradient-text-primary">
              Trusted by Leading Innovators
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Partnering with industry leaders to drive digital transformation
              and create lasting impact
            </p>
          </div>

          {/* Grid of client logos */}
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-8">
            {clientLogos.map((client, index) => (
              <div key={index} className="group">
                <div className="flex items-center justify-center p-6 bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl h-20 transition-all duration-300 hover:scale-105">
                  <img
                    src={client.logo}
                    alt={client.name}
                    className="w-full h-12 object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300 opacity-60 group-hover:opacity-100"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* 6. Testimonials */}
      <section
        className="py-20 lg:py-32"
        style={{
          background: "linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%)",
        }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl lg:text-6xl font-display font-bold mb-6 gradient-text-primary">
              What Our Clients Say
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Real results from real partnerships that have transformed
              businesses worldwide
            </p>
          </div>

          {/* 3-column responsive grid */}
          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div
                key={index}
                className="bg-white/10 backdrop-blur-md border border-white/15 rounded-3xl p-8 group hover:bg-white/15 transition-all duration-300"
              >
                {/* Client photo or logo */}
                <div className="flex items-center gap-4 mb-6">
                  <div className="flex-shrink-0">
                    <img
                      src={testimonial.avatar}
                      alt={testimonial.author}
                      className="w-14 h-14 rounded-full object-cover border-2 border-purple-500/30"
                    />
                  </div>
                  <div className="flex-shrink-0">
                    <img
                      src={testimonial.logo}
                      alt={testimonial.company}
                      className="w-12 h-12 object-contain opacity-60"
                    />
                  </div>
                  <div className="flex items-center gap-1">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star
                        key={i}
                        className="w-4 h-4 fill-yellow-400 text-yellow-400"
                      />
                    ))}
                  </div>
                </div>

                {/* Quote icon */}
                <Quote className="w-10 h-10 text-purple-400 mb-6" />

                {/* Quote text */}
                <p className="text-gray-300 mb-6 leading-relaxed italic">
                  "{testimonial.quote}"
                </p>

                {/* Attribution */}
                <div className="border-t border-white/10 pt-6">
                  <div className="font-display font-bold text-lg text-white">
                    {testimonial.author}
                  </div>
                  <div className="text-gray-400 mb-1">{testimonial.title}</div>
                  <div className="text-purple-400 font-semibold">
                    {testimonial.company}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* 7. Final CTA Section */}
      <section className="py-20 lg:py-32 bg-black relative overflow-hidden">
        {/* Background */}
        <div className="absolute inset-0 bg-gradient-to-r from-purple-900/10 via-purple-600/10 to-purple-900/10"></div>

        {/* Content */}
        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="inline-flex items-center gap-4 mb-8">
            <Sparkles className="w-10 h-10 text-purple-400 animate-pulse" />
            <h2 className="text-4xl sm:text-5xl lg:text-6xl font-display font-bold gradient-text-primary">
              Ready to get started?
            </h2>
            <Sparkles className="w-10 h-10 text-purple-400 animate-pulse" />
          </div>
          <p className="text-xl lg:text-2xl text-gray-300 mb-10 max-w-3xl mx-auto leading-relaxed">
            Join hundreds of companies that have transformed their business with
            our cutting-edge solutions. Let's build the future together and
            create something extraordinary.
          </p>

          {/* CTA Button */}
          <button
            onClick={scrollToContact}
            className="btn-modern text-xl px-10 py-5 group"
          >
            Let's Connect
            <ArrowRight className="ml-3 w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" />
          </button>
        </div>
      </section>
    </div>
  );
};

export default AboutUsPage;
