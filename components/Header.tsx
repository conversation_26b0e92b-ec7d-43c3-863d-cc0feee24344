import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "./ui/button";
import { Sheet, SheetContent, SheetTrigger } from "./ui/sheet";
import { Menu, ChevronDown, Zap } from "lucide-react";

const Header = () => {
  const navigate = useNavigate();
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (target && !target.closest(".dropdown-container")) {
        setActiveDropdown(null);
      }
    };

    window.addEventListener("scroll", handleScroll);
    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      window.removeEventListener("scroll", handleScroll);
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleDropdownToggle = (dropdown: string) => {
    setActiveDropdown(activeDropdown === dropdown ? null : dropdown);
  };

  const handleLogoClick = () => {
    navigate("/");
  };

  const handleContactClick = () => {
    navigate("/contact");
  };

  const handleCompanyClick = (href: string) => {
    navigate(href);
    setActiveDropdown(null);
  };

  const handlePortfolioClick = () => {
    navigate("/portfolio");
  };

  const handleServiceClick = (href: string) => {
    navigate(href);
    setActiveDropdown(null);
  };

  const companyDropdown = [
    {
      label: "About Us",
      description: "Our story, mission, and team",
      href: "/about-us",
    },
    {
      label: "Contact",
      description: "Get in touch with our team",
      href: "/contact",
    },
  ];

  const servicesDropdown = [
    // AI & Intelligence Services
    {
      label: "AI Services",
      href: "/ai-services",
      category: "Intelligence",
    },
    {
      label: "Machine Learning",
      href: "/ai-services",
      category: "Intelligence",
    },
    {
      label: "Data Analytics",
      href: "/ai-services",
      category: "Intelligence",
    },
    {
      label: "Computer Vision",
      href: "/ai-services",
      category: "Intelligence",
    },

    // Development Services
    {
      label: "Development Services",
      href: "/development-services",
      category: "Development",
    },
    {
      label: "Web Development",
      href: "/development-services",
      category: "Development",
    },
    {
      label: "Mobile Apps",
      href: "/development-services",
      category: "Development",
    },
    {
      label: "API Development",
      href: "/development-services",
      category: "Development",
    },

    // Infrastructure Services
    {
      label: "Cloud Services",
      href: "/cloud-services",
      category: "Infrastructure",
    },
    {
      label: "DevOps & CI/CD",
      href: "/cloud-services",
      category: "Infrastructure",
    },
    {
      label: "System Architecture",
      href: "/cloud-services",
      category: "Infrastructure",
    },
    {
      label: "Database Solutions",
      href: "/cloud-services",
      category: "Infrastructure",
    },

    // Strategy Services
    {
      label: "Digital Strategy",
      href: "/digital-strategy",
      category: "Strategy",
    },
    {
      label: "Technology Consulting",
      href: "/digital-strategy",
      category: "Strategy",
    },
    {
      label: "Digital Transformation",
      href: "/digital-strategy",
      category: "Strategy",
    },
    {
      label: "Process Automation",
      href: "/digital-strategy",
      category: "Strategy",
    },
  ];

  const groupedServices = servicesDropdown.reduce((acc, service) => {
    if (!acc[service.category]) {
      acc[service.category] = [];
    }
    acc[service.category].push(service);
    return acc;
  }, {} as Record<string, typeof servicesDropdown>);

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
        isScrolled
          ? "bg-background/95 backdrop-blur-xl border-b border-white/10 shadow-2xl"
          : "bg-transparent"
      }`}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-20">
          {/* Logo */}
          <div
            className="flex items-center space-x-3 cursor-pointer group transition-all duration-300 hover:scale-105"
            onClick={handleLogoClick}
          >
            <div className="w-10 h-10 bg-gradient-to-br from-primary to-purple-600 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl group-hover:shadow-primary/25 transition-all duration-300">
              {/* <Zap className="w-6 h-6 text-white group-hover:scale-110 transition-transform duration-300" /> */}
              <img
                src="/BLACKLION-LOGO-WHITE.png"
                alt="Black Lion Logo"
                className="w-8 h-8"
              />
            </div>
            <div>
              <h1 className="font-display font-black text-white text-xl tracking-tight group-hover:text-primary transition-colors duration-300">
                Black Lion
              </h1>
              <p className="font-body text-xs text-gray-400 -mt-1 group-hover:text-gray-300 transition-colors duration-300">
                Software
              </p>
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            <nav className="hidden lg:flex items-center space-x-2">
              {/* Company Dropdown */}
              <div className="relative dropdown-container">
                <button
                  className={`flex items-center space-x-1 px-4 py-2 rounded-xl transition-all duration-300 font-display font-medium text-sm tracking-wide ${
                    activeDropdown === "company"
                      ? "text-primary bg-white/10"
                      : "text-white hover:text-primary hover:bg-white/5"
                  }`}
                  onClick={() => handleDropdownToggle("company")}
                  onMouseEnter={() => setActiveDropdown("company")}
                >
                  <span>Company</span>
                  <ChevronDown
                    className={`w-4 h-4 transition-transform duration-300 ${
                      activeDropdown === "company" ? "rotate-180" : ""
                    }`}
                  />
                </button>

                {activeDropdown === "company" && (
                  <div
                    className="absolute top-full right-0 mt-2 w-64 bg-gray-950/95 backdrop-blur-xl rounded-2xl border border-white/20 shadow-2xl py-3 z-[60] animate-fade-in-up"
                    onMouseEnter={() => setActiveDropdown("company")}
                    onMouseLeave={() => setActiveDropdown(null)}
                  >
                    {companyDropdown.map((item) => (
                      <button
                        key={item.label}
                        onClick={() => handleCompanyClick(item.href)}
                        className="block w-full text-left px-4 py-3 hover:bg-primary/15 transition-colors group"
                      >
                        <div className="text-white font-display font-medium text-sm group-hover:text-primary">
                          {item.label}
                        </div>
                        <div className="text-gray-300 font-body text-xs mt-1">
                          {item.description}
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Portfolio Link */}
              <button
                className="px-4 py-2 rounded-xl transition-all duration-300 font-display font-medium text-sm tracking-wide text-white hover:text-primary hover:bg-white/5"
                onClick={handlePortfolioClick}
              >
                Portfolio
              </button>

              {/* Services Mega Menu */}
              <div className="relative dropdown-container">
                <button
                  className={`flex items-center space-x-1 px-4 py-2 rounded-xl transition-all duration-300 font-display font-medium text-sm tracking-wide ${
                    activeDropdown === "services"
                      ? "text-primary bg-white/10"
                      : "text-white hover:text-primary hover:bg-white/5"
                  }`}
                  onClick={() => handleDropdownToggle("services")}
                  onMouseEnter={() => setActiveDropdown("services")}
                >
                  <span>Services</span>
                  <ChevronDown
                    className={`w-4 h-4 transition-transform duration-300 ${
                      activeDropdown === "services" ? "rotate-180" : ""
                    }`}
                  />
                </button>

                {activeDropdown === "services" && (
                  <div
                    className="absolute top-full right-0 mt-2 w-[600px] bg-gray-950/95 backdrop-blur-xl rounded-2xl border border-white/20 shadow-2xl p-6 z-[60] animate-fade-in-up"
                    onMouseEnter={() => setActiveDropdown("services")}
                    onMouseLeave={() => setActiveDropdown(null)}
                  >
                    <div className="grid grid-cols-2 gap-6">
                      {Object.entries(groupedServices).map(
                        ([category, services]) => (
                          <div key={category}>
                            <h4 className="text-primary font-display font-semibold text-sm mb-3 uppercase tracking-wide">
                              {category}
                            </h4>
                            <div className="space-y-2">
                              {services.map((service) => (
                                <button
                                  key={service.label}
                                  onClick={() =>
                                    handleServiceClick(service.href)
                                  }
                                  className="block w-full text-left px-3 py-2 text-sm text-gray-200 hover:text-white hover:bg-primary/15 rounded-lg transition-colors font-body"
                                >
                                  {service.label}
                                </button>
                              ))}
                            </div>
                          </div>
                        )
                      )}
                    </div>
                  </div>
                )}
              </div>
            </nav>

            {/* CTA Button */}
            <Button
              className="bg-primary hover:bg-primary/90 text-white font-display font-semibold px-6 py-2.5 rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-primary/25"
              onClick={handleContactClick}
            >
              Get Started
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
            <SheetTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="lg:hidden text-white hover:bg-white/10"
              >
                <Menu className="w-6 h-6" />
              </Button>
            </SheetTrigger>
            <SheetContent
              side="right"
              className="w-80 bg-gray-950/95 backdrop-blur-xl border-white/20"
            >
              <div className="flex flex-col h-full">
                {/* Mobile Header */}
                <div className="flex items-center justify-between p-6 border-b border-white/10">
                  <div
                    className="flex items-center space-x-2 cursor-pointer group"
                    onClick={() => {
                      handleLogoClick();
                      setIsMobileMenuOpen(false);
                    }}
                  >
                    <div className="w-8 h-8 bg-gradient-to-br from-primary to-purple-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <Zap className="w-5 h-5 text-white" />
                    </div>
                    <span className="font-display font-bold text-white group-hover:text-primary transition-colors duration-300">
                      Black Lion
                    </span>
                  </div>
                </div>

                {/* Mobile Navigation */}
                <nav className="flex-1 p-6 space-y-6">
                  {/* Company Section */}
                  <div>
                    <h3 className="font-display font-semibold text-white mb-3">
                      Company
                    </h3>
                    <div className="space-y-2 pl-4">
                      {companyDropdown.map((item) => (
                        <button
                          key={item.label}
                          onClick={() => {
                            handleCompanyClick(item.href);
                            setIsMobileMenuOpen(false);
                          }}
                          className="block w-full text-left text-gray-300 hover:text-primary transition-colors font-body text-sm"
                        >
                          {item.label}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Portfolio Section */}
                  <div>
                    <button
                      onClick={() => {
                        handlePortfolioClick();
                        setIsMobileMenuOpen(false);
                      }}
                      className="font-display font-semibold text-white hover:text-primary transition-colors"
                    >
                      Portfolio
                    </button>
                  </div>

                  {/* Services Section */}
                  <div>
                    <h3 className="font-display font-semibold text-white mb-3">
                      Services
                    </h3>
                    <div className="space-y-4 pl-4">
                      {Object.entries(groupedServices).map(
                        ([category, services]) => (
                          <div key={category}>
                            <h4 className="font-display font-medium text-primary text-sm mb-2 uppercase tracking-wide">
                              {category}
                            </h4>
                            <div className="space-y-1 pl-2">
                              {services.map((service) => (
                                <button
                                  key={service.label}
                                  onClick={() => {
                                    handleServiceClick(service.href);
                                    setIsMobileMenuOpen(false);
                                  }}
                                  className="block w-full text-left text-gray-300 hover:text-white transition-colors font-body text-sm"
                                >
                                  {service.label}
                                </button>
                              ))}
                            </div>
                          </div>
                        )
                      )}
                    </div>
                  </div>
                </nav>

                {/* Mobile CTA */}
                <div className="p-6 border-t border-white/10">
                  <Button
                    className="w-full bg-primary hover:bg-primary/90 text-white font-display font-semibold py-3 rounded-xl"
                    onClick={() => {
                      setIsMobileMenuOpen(false);
                      handleContactClick();
                    }}
                  >
                    Get Started
                  </Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
};

export default Header;
