@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap");

:root {
  --font-size: 14px;
  --background: #fafafa;
  --foreground: #1a1a1a;
  --card: #ffffff;
  --card-foreground: #1a1a1a;
  --popover: #ffffff;
  --popover-foreground: #1a1a1a;
  --primary: #dc2626;
  --primary-foreground: #ffffff;
  --secondary: #6b7280;
  --secondary-foreground: #ffffff;
  --success: #10b981;
  --success-foreground: #ffffff;
  --muted: #6b7280;
  --muted-foreground: #9ca3af;
  --accent: #06b6d4;
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --warning: #f59e0b;
  --warning-foreground: #ffffff;
  --border: #e5e7eb;
  --input: #e5e7eb;
  --input-background: #ffffff;
  --switch-background: #e5e7eb;
  --dark-bg: #1f2937;
  --dark-foreground: #f9fafb;
  --font-weight-medium: 500;
  --font-weight-normal: 400;
  --ring: #dc2626;
  --chart-1: #dc2626;
  --chart-2: #06b6d4;
  --chart-3: #f59e0b;
  --chart-4: #10b981;
  --chart-5: #8b5cf6;
  --radius: 0.5rem;
  --sidebar: #ffffff;
  --sidebar-foreground: #1a1a1a;
  --sidebar-primary: #dc2626;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: rgba(220, 38, 38, 0.1);
  --sidebar-accent-foreground: #1a1a1a;
  --sidebar-border: #e5e7eb;
  --sidebar-ring: #dc2626;

  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-input-background: var(--input-background);
  --color-switch-background: var(--switch-background);
  --color-dark-bg: var(--dark-bg);
  --color-dark-foreground: var(--dark-foreground);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

/* Base styles without @layer directive */
* {
  border-color: var(--border);
  outline-color: rgba(var(--ring), 0.5);
}

body {
  background-color: var(--background);
  color: var(--foreground);
  font-family: "Inter", sans-serif;
}

/**
 * Base typography using Inter. This is not applied to elements which have an ancestor with a Tailwind text class.
 */
/* Typography styles without @layer directive */
:where(:not(:has([class*=" text-"]), :not(:has([class^="text-"])))) {
  h1 {
    font-family: "Inter", sans-serif;
    font-size: var(--text-2xl);
    font-weight: var(--font-weight-medium);
    line-height: 1.4;
  }

  h2 {
    font-family: "Inter", sans-serif;
    font-size: var(--text-xl);
    font-weight: var(--font-weight-medium);
    line-height: 1.4;
  }

  h3 {
    font-family: "Inter", sans-serif;
    font-size: var(--text-lg);
    font-weight: var(--font-weight-medium);
    line-height: 1.4;
  }

  h4 {
    font-family: "Inter", sans-serif;
    font-size: var(--text-base);
    font-weight: var(--font-weight-medium);
    line-height: 1.4;
  }

  p {
    font-family: "Inter", sans-serif;
    font-size: var(--text-base);
    font-weight: var(--font-weight-normal);
    line-height: 1.6;
  }

  label {
    font-family: "Inter", sans-serif;
    font-size: var(--text-base);
    font-weight: var(--font-weight-medium);
    line-height: 1.4;
  }

  button {
    font-family: "Inter", sans-serif;
    font-size: var(--text-base);
    font-weight: var(--font-weight-medium);
    line-height: 1.4;
  }

  input {
    font-family: "Inter", sans-serif;
    font-size: var(--text-base);
    font-weight: var(--font-weight-normal);
    line-height: 1.4;
  }
}

html {
  font-size: var(--font-size);
  font-family: "Inter", sans-serif;
}

/* Inter font utility */
.font-inter {
  font-family: "Inter", sans-serif;
}

/* JetBrains Mono font utility for code/metrics */
.font-mono {
  font-family: "JetBrains Mono", monospace;
}

/* Custom Animations */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes bounce-gentle {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse-soft {
  0%,
  100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes slide-in {
  0% {
    transform: translateX(-30px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}

@keyframes glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(220, 38, 38, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(220, 38, 38, 0.6);
  }
}

@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes data-flow {
  0%,
  100% {
    opacity: 0.3;
    transform: translateX(-10px);
  }
  50% {
    opacity: 1;
    transform: translateX(10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-bounce-gentle {
  animation: bounce-gentle 2s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

.animate-pulse-soft {
  animation: pulse-soft 3s ease-in-out infinite;
}

.animate-slide-in {
  animation: slide-in 0.8s ease-out;
}

.animate-shimmer {
  animation: shimmer 2s linear infinite;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(220, 38, 38, 0.1),
    transparent
  );
  background-size: 200px 100%;
}

.animate-glow {
  animation: glow 3s ease-in-out infinite;
}

.animate-spin-slow {
  animation: spin-slow 8s linear infinite;
}

.animate-data-flow {
  animation: data-flow 2s ease-in-out infinite;
}

/* Gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, #dc2626, #06b6d4);
}

.gradient-secondary {
  background: linear-gradient(135deg, #6b7280, #dc2626);
}

.gradient-success {
  background: linear-gradient(135deg, #10b981, #34d399);
}

.gradient-warning {
  background: linear-gradient(135deg, #f59e0b, #fbbf24);
}

.gradient-danger {
  background: linear-gradient(135deg, #ef4444, #f87171);
}

/* Modern card styling */
.modern-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(220, 38, 38, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.modern-card-hover {
  transition: all 0.3s ease;
}

.modern-card-hover:hover {
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(220, 38, 38, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
}

/* Dark theme card */
.dark-card {
  background: rgba(31, 41, 55, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(220, 38, 38, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.dark-card-hover {
  transition: all 0.3s ease;
}

.dark-card-hover:hover {
  background: rgba(31, 41, 55, 1);
  border: 1px solid rgba(220, 38, 38, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
}

/* Background patterns */
.bg-tech-gradient {
  background: linear-gradient(135deg, #fafafa 0%, #f3f4f6 50%, #e5e7eb 100%);
}

.bg-accent-gradient {
  background: linear-gradient(135deg, #dc2626 0%, #06b6d4 100%);
}

.bg-dark-gradient {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

/* Metric display styles */
.metric-positive {
  color: #10b981;
}

.metric-negative {
  color: #ef4444;
}

.metric-warning {
  color: #f59e0b;
}

.metric-neutral {
  color: #6b7280;
}

/* Social platform colors */
.spotify-green {
  color: #1db954;
}

.youtube-red {
  color: #ff0000;
}

.instagram-gradient {
  background: linear-gradient(45deg, #f58529, #dd2a7b, #8134af, #515bd4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.tiktok-black {
  color: #000000;
}

.twitter-blue {
  color: #1da1f2;
}
