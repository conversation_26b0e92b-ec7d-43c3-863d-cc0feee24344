@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800;900&family=Open+Sans:wght@300;400;500;600;700;800&display=swap");

:root {
  --font-size: 16px;
  --background: 10 10 10;
  --foreground: 255 255 255;
  --card: 26 26 26;
  --card-foreground: 255 255 255;
  --popover: 26 26 26;
  --popover-foreground: 255 255 255;
  --primary: 84 37 176;
  --primary-foreground: 255 255 255;
  --secondary: 42 42 42;
  --secondary-foreground: 255 255 255;
  --muted: 42 42 42;
  --muted-foreground: 160 160 160;
  --accent: 84 37 176;
  --accent-foreground: 255 255 255;
  --destructive: 220 38 38;
  --destructive-foreground: 255 255 255;
  --border: 42 42 42;
  --input: 42 42 42;
  --input-background: 26 26 26;
  --switch-background: 42 42 42;
  --font-weight-medium: 500;
  --font-weight-normal: 400;
  --ring: 84 37 176;
  --chart-1: 84 37 176;
  --chart-2: 139 92 246;
  --chart-3: 168 85 247;
  --chart-4: 192 132 252;
  --chart-5: 221 214 254;
  --radius: 0.75rem;
  --sidebar: 26 26 26;
  --sidebar-foreground: 255 255 255;
  --sidebar-primary: 84 37 176;
  --sidebar-primary-foreground: 255 255 255;
  --sidebar-accent: 42 42 42;
  --sidebar-accent-foreground: 255 255 255;
  --sidebar-border: 42 42 42;
  --sidebar-ring: 84 37 176;

  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-input-background: var(--input-background);
  --color-switch-background: var(--switch-background);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  /* Balanced Typography Scale - Professional & Readable */
  --font-size-xs: 0.75rem; /* 12px */
  --font-size-sm: 0.875rem; /* 14px */
  --font-size-base: 1rem; /* 16px */
  --font-size-lg: 1.125rem; /* 18px */
  --font-size-xl: 1.25rem; /* 20px */
  --font-size-2xl: 1.5rem; /* 24px */
  --font-size-3xl: 1.875rem; /* 30px */
  --font-size-4xl: 2.25rem; /* 36px */
  --font-size-5xl: 3rem; /* 48px */
  --font-size-6xl: 3.75rem; /* 60px */
  --font-size-7xl: 4.5rem; /* 72px */
  --font-size-8xl: 6rem; /* 96px */
  --font-size-9xl: 8rem; /* 128px */
}

.dark {
  --background: #0a0a0a;
  --foreground: #ffffff;
  --card: #1a1a1a;
  --card-foreground: #ffffff;
  --popover: #1a1a1a;
  --popover-foreground: #ffffff;
  --primary: #5425b0;
  --primary-foreground: #ffffff;
  --secondary: #2a2a2a;
  --secondary-foreground: #ffffff;
  --muted: #2a2a2a;
  --muted-foreground: #a0a0a0;
  --accent: #5425b0;
  --accent-foreground: #ffffff;
  --destructive: #dc2626;
  --destructive-foreground: #ffffff;
  --border: #2a2a2a;
  --input: #2a2a2a;
  --ring: #5425b0;
  --chart-1: #5425b0;
  --chart-2: #8b5cf6;
  --chart-3: #a855f7;
  --chart-4: #c084fc;
  --chart-5: #ddd6fe;
  --sidebar: #1a1a1a;
  --sidebar-foreground: #ffffff;
  --sidebar-primary: #5425b0;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #2a2a2a;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: #2a2a2a;
  --sidebar-ring: #5425b0;

  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-input-background: var(--input-background);
  --color-switch-background: var(--switch-background);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  /* Balanced Typography Scale - Professional & Readable */
  --font-size-xs: 0.75rem; /* 12px */
  --font-size-sm: 0.875rem; /* 14px */
  --font-size-base: 1rem; /* 16px */
  --font-size-lg: 1.125rem; /* 18px */
  --font-size-xl: 1.25rem; /* 20px */
  --font-size-2xl: 1.5rem; /* 24px */
  --font-size-3xl: 1.875rem; /* 30px */
  --font-size-4xl: 2.25rem; /* 36px */
  --font-size-5xl: 3rem; /* 48px */
  --font-size-6xl: 3.75rem; /* 60px */
  --font-size-7xl: 4.5rem; /* 72px */
  --font-size-8xl: 6rem; /* 96px */
  --font-size-9xl: 8rem; /* 128px */
}

* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: "Open Sans", sans-serif;
  font-feature-settings: "cv02", "cv03", "cv04", "cv11";
}

html {
  scroll-behavior: smooth;
}

/* Override Tailwind text size classes with balanced sizes */
.text-xs {
  font-size: var(--font-size-xs) !important;
}
.text-sm {
  font-size: var(--font-size-sm) !important;
}
.text-base {
  font-size: var(--font-size-base) !important;
}
.text-lg {
  font-size: var(--font-size-lg) !important;
}
.text-xl {
  font-size: var(--font-size-xl) !important;
}
.text-2xl {
  font-size: var(--font-size-2xl) !important;
}
.text-3xl {
  font-size: var(--font-size-3xl) !important;
}
.text-4xl {
  font-size: var(--font-size-4xl) !important;
}
.text-5xl {
  font-size: var(--font-size-5xl) !important;
}
.text-6xl {
  font-size: var(--font-size-6xl) !important;
}
.text-7xl {
  font-size: var(--font-size-7xl) !important;
}
.text-8xl {
  font-size: var(--font-size-8xl) !important;
}
.text-9xl {
  font-size: var(--font-size-9xl) !important;
}

/* Modern Typography */
.font-display {
  font-family: "Montserrat", sans-serif;
  font-feature-settings: "ss01", "ss02";
}

.font-body {
  font-family: "Open Sans", sans-serif;
  font-feature-settings: "cv02", "cv03", "cv04", "cv11";
}

/* Advanced Animation System */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(24px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-24px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-24px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(24px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.96);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(32px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-6px);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse-glow {
  0%,
  100% {
    box-shadow: 0 0 15px rgba(84, 37, 176, 0.2);
  }
  50% {
    box-shadow: 0 0 25px rgba(84, 37, 176, 0.35);
  }
}

@keyframes gradient-shift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes shimmer {
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
}

@keyframes particles {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}

/* Animation Classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-fade-in-down {
  animation: fadeInDown 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-scale-in {
  animation: scaleIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-slide-in {
  animation: slideIn 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-rotate {
  animation: rotate 20s linear infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-gradient {
  background-size: 400% 400%;
  animation: gradient-shift 3s ease infinite;
}

.animate-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  background-size: 1000px 100%;
  animation: shimmer 2s infinite;
}

/* Modern Gradient Backgrounds */
.gradient-hero {
  background: radial-gradient(ellipse at center, #1a1a1a 0%, #0a0a0a 70%);
  position: relative;
  overflow: hidden;
}

.gradient-hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    rgba(84, 37, 176, 0.08) 0%,
    transparent 50%,
    rgba(139, 92, 246, 0.04) 100%
  );
  animation: gradient-shift 8s ease infinite;
  background-size: 400% 400%;
}

.gradient-section {
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  position: relative;
}

.gradient-card {
  background: linear-gradient(
    135deg,
    rgba(26, 26, 26, 0.8) 0%,
    rgba(42, 42, 42, 0.4) 100%
  );
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.gradient-primary {
  background: linear-gradient(135deg, #5425b0 0%, #8b5cf6 100%);
}

.gradient-text {
  background: linear-gradient(135deg, #ffffff 0%, #a0a0a0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-primary {
  background: linear-gradient(135deg, #5425b0 0%, #8b5cf6 50%, #a855f7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glassmorphism Effects */
.glass {
  background: rgba(26, 26, 26, 0.25);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-strong {
  background: rgba(26, 26, 26, 0.4);
  backdrop-filter: blur(24px);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

/* Modern Hover Effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.hover-lift:hover {
  transform: translateY(-4px) scale(1.01);
}

.hover-glow {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
}

.hover-glow::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 1px;
  background: linear-gradient(
    135deg,
    rgba(84, 37, 176, 0.4),
    rgba(139, 92, 246, 0.2)
  );
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.hover-glow:hover::before {
  opacity: 1;
}

.hover-glow:hover {
  box-shadow: 0 10px 25px rgba(84, 37, 176, 0.2);
}

.hover-scale {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* Particle System */
.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 3px;
  height: 3px;
  background: rgba(84, 37, 176, 0.25);
  border-radius: 50%;
  animation: particles 15s linear infinite;
}

/* Scroll Animations */
.scroll-reveal {
  opacity: 0;
  transform: translateY(24px);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* Modern Button Styles */
.btn-modern {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #5425b0 0%, #8b5cf6 100%);
  color: #ffffff;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
  font-family: "Montserrat", sans-serif;
  font-size: 0.875rem;
  line-height: 1.4;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 2px 10px rgba(84, 37, 176, 0.2);
}

.btn-modern::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.15),
    transparent
  );
  transition: left 0.4s ease;
}

.btn-modern:hover::before {
  left: 100%;
}

.btn-modern:hover {
  transform: translateY(-1px) scale(1.01);
  box-shadow: 0 4px 15px rgba(84, 37, 176, 0.3);
}

.btn-modern:active {
  transform: translateY(0) scale(0.99);
}

.btn-secondary {
  position: relative;
  background: rgba(26, 26, 26, 0.6);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  padding: 12px 20px;
  font-weight: 500;
  font-family: "Montserrat", sans-serif;
  font-size: 0.875rem;
  line-height: 1.4;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(20px);
}

.btn-secondary:hover {
  background: rgba(84, 37, 176, 0.08);
  border-color: rgba(84, 37, 176, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn-secondary:active {
  transform: translateY(0);
}

/* Button Responsive Design */
@media (max-width: 640px) {
  .btn-modern,
  .btn-secondary {
    padding: 10px 18px;
    font-size: 0.8125rem;
    width: auto;
    min-width: 140px;
  }

  .btn-modern {
    box-shadow: 0 2px 8px rgba(84, 37, 176, 0.15);
  }

  .btn-modern:hover {
    transform: translateY(-1px) scale(1.005);
    box-shadow: 0 3px 12px rgba(84, 37, 176, 0.25);
  }
}

/* Balanced Typography Scale - Professional & Readable */
:where(:not(:has([class*=" text-"]), :not(:has([class^="text-"])))) {
  h1 {
    font-size: 1.875rem;
    font-weight: 700;
    line-height: 1.2;
    font-family: "Montserrat", sans-serif;
    letter-spacing: -0.015em;
    margin-bottom: 0.75rem;
  }

  h2 {
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 1.3;
    font-family: "Montserrat", sans-serif;
    letter-spacing: -0.01em;
    margin-bottom: 0.625rem;
  }

  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    line-height: 1.4;
    font-family: "Montserrat", sans-serif;
    letter-spacing: -0.005em;
    margin-bottom: 0.5rem;
  }

  h4 {
    font-size: 1.125rem;
    font-weight: 600;
    line-height: 1.4;
    font-family: "Montserrat", sans-serif;
    margin-bottom: 0.5rem;
  }

  h5 {
    font-size: 1rem;
    font-weight: 500;
    line-height: 1.5;
    font-family: "Montserrat", sans-serif;
    margin-bottom: 0.375rem;
  }

  h6 {
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.5;
    font-family: "Montserrat", sans-serif;
    margin-bottom: 0.375rem;
  }

  p {
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.6;
    font-family: "Open Sans", sans-serif;
    margin-bottom: 0.75rem;
  }

  label {
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.4;
    font-family: "Open Sans", sans-serif;
  }

  button {
    font-size: 0.875rem;
    font-weight: 600;
    line-height: 1.4;
    font-family: "Montserrat", sans-serif;
  }

  input {
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.4;
    font-family: "Open Sans", sans-serif;
  }
}

/* Mobile Responsive Typography */
@media (max-width: 768px) {
  :where(:not(:has([class*=" text-"]), :not(:has([class^="text-"])))) {
    h1 {
      font-size: 1.5rem;
      margin-bottom: 0.625rem;
    }

    h2 {
      font-size: 1.25rem;
      margin-bottom: 0.5rem;
    }

    h3 {
      font-size: 1.125rem;
      margin-bottom: 0.5rem;
    }

    h4 {
      font-size: 1rem;
      margin-bottom: 0.375rem;
    }

    p {
      font-size: 0.9375rem;
      margin-bottom: 0.625rem;
    }
  }

  /* Mobile text size overrides - balanced for readability */
  .text-4xl {
    font-size: 1.75rem !important;
  } /* 28px */
  .text-5xl {
    font-size: 2.25rem !important;
  } /* 36px */
  .text-6xl {
    font-size: 2.75rem !important;
  } /* 44px */
  .text-7xl {
    font-size: 3.25rem !important;
  } /* 52px */
}

/* Tablet Responsive Typography */
@media (min-width: 769px) and (max-width: 1024px) {
  :where(:not(:has([class*=" text-"]), :not(:has([class^="text-"])))) {
    h1 {
      font-size: 1.75rem;
    }

    h2 {
      font-size: 1.375rem;
    }
  }

  /* Tablet text size overrides */
  .text-4xl {
    font-size: 2rem !important;
  } /* 32px */
  .text-5xl {
    font-size: 2.5rem !important;
  } /* 40px */
  .text-6xl {
    font-size: 3.25rem !important;
  } /* 52px */
  .text-7xl {
    font-size: 4rem !important;
  } /* 64px */
}

/* Utility Classes */
.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

html {
  font-size: var(--font-size);
}

/* Professional Spacing and Layout */
.section-spacing {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

@media (min-width: 768px) {
  .section-spacing {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }
}

@media (min-width: 1024px) {
  .section-spacing {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }
}

.container-spacing {
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-spacing {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-spacing {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Card and Component Spacing */
.card-spacing {
  padding: 1.5rem;
}

@media (min-width: 768px) {
  .card-spacing {
    padding: 2rem;
  }
}

.element-spacing {
  margin-bottom: 1rem;
}

@media (min-width: 768px) {
  .element-spacing {
    margin-bottom: 1.25rem;
  }
}

.text-spacing {
  margin-bottom: 0.75rem;
}

/* Grid and Layout Improvements */
.grid-compact {
  gap: 1rem;
}

@media (min-width: 768px) {
  .grid-compact {
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .grid-compact {
    gap: 2rem;
  }
}

/* Scrollbar Utilities */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
